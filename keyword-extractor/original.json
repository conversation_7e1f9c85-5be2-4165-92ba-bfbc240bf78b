[{"label": "*** Settings ***", "kind": "Snippet", "insertText": "*** Settings ***\n", "documentation": "Settings section"}, {"label": "*** Variables ***", "kind": "Snippet", "insertText": "*** Variables ***\n", "documentation": "Variables section"}, {"label": "*** Test Cases ***", "kind": "Snippet", "insertText": "*** Test Cases ***\n", "documentation": "Test Cases section"}, {"label": "*** Keywords ***", "kind": "Snippet", "insertText": "*** Keywords ***\n", "documentation": "Keywords section"}, {"label": "*** Tasks ***", "kind": "Snippet", "insertText": "*** Tasks ***\n", "documentation": "Tasks section"}, {"label": "Library", "kind": "Keyword", "insertText": "Library    ${1:LibraryName}", "documentation": "Import a library"}, {"label": "Resource", "kind": "Keyword", "insertText": "Resource    ${1:resource_file.robot}", "documentation": "Import a resource file"}, {"label": "Variables", "kind": "Keyword", "insertText": "Variables    ${1:variables_file.py}", "documentation": "Import variables from a file"}, {"label": "Documentation", "kind": "Keyword", "insertText": "Documentation    ${1:Documentation text}", "documentation": "Document a test suite or keyword"}, {"label": "Suite Setup", "kind": "Keyword", "insertText": "Suite Setup    ${1:Keyword Name}", "documentation": "Setup executed before any test case"}, {"label": "Suite Teardown", "kind": "Keyword", "insertText": "Suite Teardown    ${1:Keyword Name}", "documentation": "Teardown executed after all test cases"}, {"label": "Test Setup", "kind": "Keyword", "insertText": "Test Setup    ${1:Keyword Name}", "documentation": "Setup executed before each test case"}, {"label": "Test Teardown", "kind": "Keyword", "insertText": "Test Teardown    ${1:Keyword Name}", "documentation": "Teardown executed after each test case"}, {"label": "Test Template", "kind": "Keyword", "insertText": "Test Template    ${1:Keyword Name}", "documentation": "Default template keyword for test cases"}, {"label": "Test Timeout", "kind": "Keyword", "insertText": "Test Timeout    ${1:timeout}", "documentation": "Default timeout for test cases"}, {"label": "Force Tags", "kind": "Keyword", "insertText": "Force Tags    ${1:tag1}    ${2:tag2}", "documentation": "Tags added to all test cases"}, {"label": "Default Tags", "kind": "Keyword", "insertText": "Default Tags    ${1:tag1}    ${2:tag2}", "documentation": "Default tags for test cases"}, {"label": "Log", "kind": "Function", "insertText": "Log    ${1:message}", "documentation": "Logs the given message with the given level."}, {"label": "Log To Console", "kind": "Function", "insertText": "Log To Console    ${1:message}", "documentation": "Logs the given message to the console."}, {"label": "Sleep", "kind": "Function", "insertText": "Sleep    ${1:time}", "documentation": "Pauses the test execution for the given time."}, {"label": "Should Be Equal", "kind": "Function", "insertText": "Should Be Equal    ${1:first}    ${2:second}", "documentation": "Fails if the given objects are not equal."}, {"label": "Should Not Be Equal", "kind": "Function", "insertText": "Should Not Be Equal    ${1:first}    ${2:second}", "documentation": "Fails if the given objects are equal."}, {"label": "Should Contain", "kind": "Function", "insertText": "Should Contain    ${1:container}    ${2:item}", "documentation": "Fails if container does not contain item."}, {"label": "Should Not Contain", "kind": "Function", "insertText": "Should Not Contain    ${1:container}    ${2:item}", "documentation": "Fails if container contains item."}, {"label": "Should Be True", "kind": "Function", "insertText": "Should Be True    ${1:condition}", "documentation": "Fails if the condition is not true."}, {"label": "Should Be False", "kind": "Function", "insertText": "Should Be False    ${1:condition}", "documentation": "Fails if the condition is not false."}, {"label": "Run Keyword", "kind": "Function", "insertText": "Run Keyword    ${1:name}    ${2:args}", "documentation": "Executes the given keyword with the given arguments."}, {"label": "Run Keyword If", "kind": "Function", "insertText": "Run Keyword If    ${1:condition}    ${2:name}    ${3:args}", "documentation": "Runs the given keyword with the given arguments if the condition is true."}, {"label": "Wait Until Keyword Succeeds", "kind": "Function", "insertText": "Wait Until Keyword Succeeds    ${1:timeout}    ${2:retry_interval}    ${3:name}    ${4:args}", "documentation": "Runs the specified keyword and retries if it fails."}, {"label": "Set Variable", "kind": "Function", "insertText": "Set Variable    ${1:value}", "documentation": "Returns the given values as-is."}, {"label": "Create List", "kind": "Function", "insertText": "Create List    ${1:items}", "documentation": "Creates a list containing the given items."}, {"label": "Create Dictionary", "kind": "Function", "insertText": "Create Dictionary    ${1:key1}=${2:value1}    ${3:key2}=${4:value2}", "documentation": "Creates a dictionary based on the given items."}, {"label": "Open Browser", "kind": "Function", "insertText": "Open Browser    ${1:url}    ${2:browser}", "documentation": "Opens a new browser instance to the given URL."}, {"label": "Close Browser", "kind": "Function", "insertText": "Close Browser", "documentation": "Closes the current browser."}, {"label": "Input Text", "kind": "Function", "insertText": "Input Text    ${1:locator}    ${2:text}", "documentation": "Types the given text into the text field identified by locator."}, {"label": "<PERSON>lick Element", "kind": "Function", "insertText": "Click Element    ${1:locator}", "documentation": "Clicks the element identified by locator."}, {"label": "Wait Until Element Is Visible", "kind": "Function", "insertText": "Wait Until Element Is Visible    ${1:locator}    ${2:timeout}=${3:None}", "documentation": "Waits until the element specified by locator is visible."}, {"label": "Element Should Be Visible", "kind": "Function", "insertText": "Element Should Be Visible    ${1:locator}", "documentation": "Verifies that the element specified by locator is visible."}, {"label": "Element Should Contain", "kind": "Function", "insertText": "Element Should Contain    ${1:locator}    ${2:text}", "documentation": "Verifies that element contains text."}, {"label": "Page Should Contain", "kind": "Function", "insertText": "Page Should Contain    ${1:text}", "documentation": "Verifies that current page contains text."}, {"label": "Given", "kind": "Keyword", "insertText": "Given ${1:precondition}", "documentation": "Gherkin-style precondition"}, {"label": "When", "kind": "Keyword", "insertText": "When ${1:action}", "documentation": "Gherkin-style action"}, {"label": "Then", "kind": "Keyword", "insertText": "Then ${1:expectation}", "documentation": "Gherkin-style expectation"}, {"label": "And", "kind": "Keyword", "insertText": "And ${1:additional}", "documentation": "Gherkin-style additional step"}, {"label": "But", "kind": "Keyword", "insertText": "But ${1:exception}", "documentation": "Gherkin-style exception"}, {"label": "Test Case", "kind": "Snippet", "insertText": "${1:Test Case Name}\n    [Documentation]    ${2:Description}\n    [Tags]    ${3:tag1}\n    ${4:Step 1}\n    ${5:Step 2}", "documentation": "Basic test case structure"}, {"label": "Keyword", "kind": "Snippet", "insertText": "${1:Keyword Name}\n    [Arguments]    ${2:\\$\\{arg1\\}}    ${3:\\$\\{arg2\\}}\n    [Documentation]    ${4:Description}\n    ${5:Step 1}\n    ${6:Step 2}\n    [Return]    ${7:\\$\\{result\\}}", "documentation": "Basic keyword structure"}, {"label": "[Arguments]", "kind": "Keyword", "insertText": "[Arguments]    ${1:\\$\\{arg1\\}}    ${2:\\$\\{arg2\\}}", "documentation": "Define arguments for a keyword"}, {"label": "[Documentation]", "kind": "Keyword", "insertText": "[Documentation]    ${1:Description}", "documentation": "Document a test case or keyword"}, {"label": "[Tags]", "kind": "Keyword", "insertText": "[Tags]    ${1:tag1}    ${2:tag2}", "documentation": "Add tags to a test case"}, {"label": "[Setup]", "kind": "Keyword", "insertText": "[Setup]    ${1:Keyword Name}", "documentation": "Setup for a test case or keyword"}, {"label": "[Teardown]", "kind": "Keyword", "insertText": "[Teardown]    ${1:Keyword Name}", "documentation": "Teardown for a test case or keyword"}, {"label": "[Template]", "kind": "Keyword", "insertText": "[Template]    ${1:Keyword Name}", "documentation": "Template keyword for a test case"}, {"label": "[Timeout]", "kind": "Keyword", "insertText": "[Timeout]    ${1:timeout}", "documentation": "Timeout for a test case or keyword"}, {"label": "[Return]", "kind": "Keyword", "insertText": "[Return]    ${1:\\$\\{value\\}}", "documentation": "Return value from a keyword"}]