#!/usr/bin/env node

console.log('🧪 Testing AWS Device Farm API Request Formats');
console.log('==============================================');

function testAPIRequestFormats() {
  console.log('📝 Testing different API request formats...\n');

  // ✅ Valid Request Format 1: Upload new app
  console.log('✅ VALID REQUEST FORMAT 1: Upload New App');
  console.log('==========================================');
  const validRequest1 = {
    testFiles: [
      {
        name: 'mobile_app.robot',
        content: `*** Settings ***
Documentation    Mobile App Test using Robot Framework and Appium
Library          AppiumLibrary

*** Variables ***
\${PLATFORM_NAME}      \${PLATFORM_NAME}
\${AUTOMATION_NAME}    \${AUTOMATION_NAME}
\${DEVICE_NAME}        \${DEVICE_NAME}
\${PLATFORM_VERSION}   \${PLATFORM_VERSION}
\${APPIUM_URL}         \${APPIUM_URL}
\${APP_PACKAGE}        \${APP_PACKAGE}
\${APP_ACTIVITY}       \${APP_ACTIVITY}

*** Test Cases ***
Launch App Test
    [Documentation]    Test app launch and basic functionality
    [Tags]    smoke
    
    Open Application    \${APPIUM_URL}
    ...    platformName=\${PLATFORM_NAME}
    ...    automationName=\${AUTOMATION_NAME}
    ...    deviceName=\${DEVICE_NAME}
    ...    platformVersion=\${PLATFORM_VERSION}
    ...    appPackage=\${APP_PACKAGE}
    ...    appActivity=\${APP_ACTIVITY}
    
    # Wait for app to load
    Sleep    5s
    
    # Take screenshot
    Capture Page Screenshot    app_launched.png
    
    # Close app
    Close Application`
      }
    ],
    appFile: {
      name: 'sample-app.apk',
      platform: 'android',
      path: '/path/to/your/app.apk'  // ✅ Provide file path for upload
    },
    runName: 'Robot Framework Test Run - Upload New App',
    devicePoolArn: 'arn:aws:devicefarm:us-west-2:123456789012:devicepool:project-id/pool-id',
    appPackage: 'com.example.app',
    appActivity: '.MainActivity'
  };

  console.log('Request Body:');
  console.log(JSON.stringify(validRequest1, null, 2));
  console.log('\n✅ This request will UPLOAD a new app file and then run tests\n');

  // ✅ Valid Request Format 2: Use existing app ARN
  console.log('✅ VALID REQUEST FORMAT 2: Use Existing App ARN');
  console.log('===============================================');
  const validRequest2 = {
    testFiles: [
      {
        name: 'login_test.robot',
        content: `*** Test Cases ***
Login Test
    [Documentation]    Test login functionality
    Log    Testing login functionality`
      }
    ],
    appFile: {
      name: 'existing-app.apk',
      platform: 'android',
      arn: 'arn:aws:devicefarm:us-west-2:123456789012:upload:project-id/app-id'  // ✅ Use existing app ARN
    },
    runName: 'Robot Framework Test Run - Existing App',
    devicePoolArn: 'arn:aws:devicefarm:us-west-2:123456789012:devicepool:project-id/pool-id',
    appPackage: 'com.example.app',
    appActivity: '.MainActivity'
  };

  console.log('Request Body:');
  console.log(JSON.stringify(validRequest2, null, 2));
  console.log('\n✅ This request will USE an existing uploaded app and run tests\n');

  // ❌ Invalid Request Format: Missing both path and ARN
  console.log('❌ INVALID REQUEST FORMAT: Missing App Path/ARN');
  console.log('===============================================');
  const invalidRequest = {
    testFiles: [
      {
        name: 'test.robot',
        content: '*** Test Cases ***\nSample Test\n    Log    Hello World'
      }
    ],
    appFile: {
      name: 'app.apk',
      platform: 'android'
      // ❌ Missing both "path" and "arn"
    },
    runName: 'Test Run',
    devicePoolArn: 'arn:aws:devicefarm:us-west-2:123456789012:devicepool:project-id/pool-id',
    appPackage: 'com.example.app',
    appActivity: '.MainActivity'
  };

  console.log('Request Body:');
  console.log(JSON.stringify(invalidRequest, null, 2));
  console.log('\n❌ This request will FAIL with error: "App file path or ARN is required"\n');

  // Expected Error Response
  console.log('❌ EXPECTED ERROR RESPONSE:');
  console.log('===========================');
  const errorResponse = {
    success: false,
    message: `App file path or ARN is required. Please provide either:
1. appFile.path - Path to app file for upload
2. appFile.arn - ARN of existing uploaded app
Example: { "appFile": { "name": "app.apk", "platform": "android", "path": "/path/to/app.apk" } }`
  };

  console.log(JSON.stringify(errorResponse, null, 2));
  console.log('\n');

  // iOS App Example
  console.log('✅ VALID REQUEST FORMAT 3: iOS App');
  console.log('==================================');
  const iOSRequest = {
    testFiles: [
      {
        name: 'ios_test.robot',
        content: `*** Test Cases ***
iOS App Test
    [Documentation]    Test iOS app functionality
    Log    Testing iOS app`
      }
    ],
    appFile: {
      name: 'sample-app.ipa',
      platform: 'ios',  // ✅ iOS platform
      path: '/path/to/your/app.ipa'  // ✅ iOS app file
    },
    runName: 'Robot Framework iOS Test Run',
    devicePoolArn: 'arn:aws:devicefarm:us-west-2:123456789012:devicepool:project-id/ios-pool-id',
    appPackage: 'com.example.iosapp',  // iOS bundle identifier
    appActivity: ''  // Not used for iOS
  };

  console.log('Request Body:');
  console.log(JSON.stringify(iOSRequest, null, 2));
  console.log('\n✅ This request will upload and test an iOS app\n');

  return {
    validRequest1,
    validRequest2,
    invalidRequest,
    iOSRequest,
    errorResponse
  };
}

function demonstrateAPIUsage() {
  console.log('🚀 API Usage Examples');
  console.log('=====================\n');

  console.log('📡 cURL Examples:');
  console.log('-----------------\n');

  console.log('1. Upload New Android App:');
  console.log('curl -X POST http://localhost:3001/api/device-farm/run-robot-tests \\');
  console.log('  -H "Content-Type: application/json" \\');
  console.log('  -H "Authorization: Bearer your-jwt-token" \\');
  console.log('  -d \'{');
  console.log('    "testFiles": [');
  console.log('      {');
  console.log('        "name": "mobile_app.robot",');
  console.log('        "content": "*** Test Cases ***\\nSample Test\\n    Log    Hello World"');
  console.log('      }');
  console.log('    ],');
  console.log('    "appFile": {');
  console.log('      "name": "app.apk",');
  console.log('      "platform": "android",');
  console.log('      "path": "/path/to/your/app.apk"');
  console.log('    },');
  console.log('    "runName": "Test Run",');
  console.log('    "devicePoolArn": "arn:aws:devicefarm:us-west-2:123456789012:devicepool:project-id/pool-id",');
  console.log('    "appPackage": "com.example.app",');
  console.log('    "appActivity": ".MainActivity"');
  console.log('  }\'\n');

  console.log('2. Use Existing App ARN:');
  console.log('curl -X POST http://localhost:3001/api/device-farm/run-robot-tests \\');
  console.log('  -H "Content-Type: application/json" \\');
  console.log('  -H "Authorization: Bearer your-jwt-token" \\');
  console.log('  -d \'{');
  console.log('    "testFiles": [...],');
  console.log('    "appFile": {');
  console.log('      "name": "app.apk",');
  console.log('      "platform": "android",');
  console.log('      "arn": "arn:aws:devicefarm:us-west-2:123456789012:upload:project-id/app-id"');
  console.log('    },');
  console.log('    "runName": "Test Run",');
  console.log('    "devicePoolArn": "arn:aws:devicefarm:us-west-2:123456789012:devicepool:project-id/pool-id"');
  console.log('  }\'\n');
}

function main() {
  console.log('🎯 This test demonstrates proper API request formats for AWS Device Farm');
  console.log('It shows valid and invalid request examples with clear explanations.\n');

  const examples = testAPIRequestFormats();
  demonstrateAPIUsage();

  console.log('📊 Summary:');
  console.log('===========');
  console.log('✅ Valid requests must include EITHER:');
  console.log('   - appFile.path (to upload new app)');
  console.log('   - appFile.arn (to use existing app)');
  console.log('❌ Requests missing both will fail with clear error message');
  console.log('🔧 Error message includes examples and instructions');
  console.log('📱 Supports both Android (.apk) and iOS (.ipa) apps');
  console.log('\n🎉 API request format validation is working correctly!');
}

main();
