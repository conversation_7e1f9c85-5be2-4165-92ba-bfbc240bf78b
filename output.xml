<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.0 (Python 3.12.6 on darwin)" generated="2025-06-12T18:03:24.710666" rpa="false" schemaversion="5">
<suite id="s1" name="Login" source="/Users/<USER>/Documents/workspace/urbox-project/urtest.js/login.robot">
<test id="s1-t1" name="Test Login" line="30">
<kw name="Open Application">
<msg time="2025-06-12T18:03:24.721690" level="FAIL">No keyword with name 'Open Application' found.</msg>
<arg>remote_url=${REMOTE_URL}</arg>
<arg>platformName=${PLATFORM_NAME}</arg>
<arg>deviceName=${DEVICE_NAME}</arg>
<arg>automationName=UiAutomator2</arg>
<arg>appPackage=${APP_PACKAGE}</arg>
<arg>appActivity=${APP_ACTIVITY}</arg>
<arg>uiautomator2ServerInstallTimeout=${TIMEOUT}</arg>
<status status="FAIL" start="2025-06-12T18:03:24.721674" elapsed="0.000040">No keyword with name 'Open Application' found.</status>
</kw>
<kw name="Set Appium Timeout">
<arg>1s</arg>
<status status="NOT RUN" start="2025-06-12T18:03:24.722081" elapsed="0.000013"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-06-12T18:03:24.722181" elapsed="0.000016"/>
</kw>
<kw name="Capture Page Screenshot">
<status status="NOT RUN" start="2025-06-12T18:03:24.722645" elapsed="0.000010"/>
</kw>
<kw name="Wait Until Element Is Visible">
<arg>xpath=//android.widget.EditText</arg>
<arg>timeout=3s</arg>
<status status="NOT RUN" start="2025-06-12T18:03:24.722964" elapsed="0.000006"/>
</kw>
<kw name="Input Text">
<arg>xpath=(//android.widget.EditText)[1]</arg>
<arg>${PHONE_NUMBER}</arg>
<status status="NOT RUN" start="2025-06-12T18:03:24.723224" elapsed="0.000006"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-06-12T18:03:24.723286" elapsed="0.000009"/>
</kw>
<kw name="Capture Page Screenshot">
<status status="NOT RUN" start="2025-06-12T18:03:24.723590" elapsed="0.000005"/>
</kw>
<kw name="Click Element">
<arg>xpath=(//android.view.ViewGroup[@clickable='true'])[2]</arg>
<status status="NOT RUN" start="2025-06-12T18:03:24.723867" elapsed="0.000005"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-06-12T18:03:24.723932" elapsed="0.000008"/>
</kw>
<kw name="Capture Page Screenshot">
<status status="NOT RUN" start="2025-06-12T18:03:24.724210" elapsed="0.000005"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-06-12T18:03:24.724253" elapsed="0.000008"/>
</kw>
<kw name="Click Element">
<arg>xpath=(//android.widget.EditText)[1]</arg>
<status status="NOT RUN" start="2025-06-12T18:03:24.724581" elapsed="0.000005"/>
</kw>
<kw name="Press Key Code">
<arg>${KEY_CODE_1}</arg>
<status status="NOT RUN" start="2025-06-12T18:03:24.724856" elapsed="0.000005"/>
</kw>
<kw name="Press Key Code">
<arg>${KEY_CODE_1}</arg>
<status status="NOT RUN" start="2025-06-12T18:03:24.725127" elapsed="0.000004"/>
</kw>
<kw name="Press Key Code">
<arg>${KEY_CODE_1}</arg>
<status status="NOT RUN" start="2025-06-12T18:03:24.725386" elapsed="0.000005"/>
</kw>
<kw name="Press Key Code">
<arg>${KEY_CODE_1}</arg>
<status status="NOT RUN" start="2025-06-12T18:03:24.725652" elapsed="0.000004"/>
</kw>
<kw name="Press Key Code">
<arg>${KEY_CODE_1}</arg>
<status status="NOT RUN" start="2025-06-12T18:03:24.725926" elapsed="0.000004"/>
</kw>
<kw name="Press Key Code">
<arg>${KEY_CODE_1}</arg>
<status status="NOT RUN" start="2025-06-12T18:03:24.726197" elapsed="0.000004"/>
</kw>
<kw name="Capture Page Screenshot">
<status status="NOT RUN" start="2025-06-12T18:03:24.726627" elapsed="0.000011"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-06-12T18:03:24.726694" elapsed="0.000008"/>
</kw>
<kw name="Click Element">
<arg>xpath=//android.widget.Button[@resource-id="com.android.permissioncontroller:id/permission_allow_button"]</arg>
<status status="NOT RUN" start="2025-06-12T18:03:24.727002" elapsed="0.000006"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-06-12T18:03:24.727049" elapsed="0.000005"/>
</kw>
<kw name="Capture Page Screenshot">
<status status="NOT RUN" start="2025-06-12T18:03:24.727341" elapsed="0.000006"/>
</kw>
<kw name="Click Element">
<arg>//android.widget.TextView[@text="Tiếp tục"]</arg>
<status status="NOT RUN" start="2025-06-12T18:03:24.727616" elapsed="0.000005"/>
</kw>
<kw name="Capture Page Screenshot">
<status status="NOT RUN" start="2025-06-12T18:03:24.727904" elapsed="0.000006"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-06-12T18:03:24.727944" elapsed="0.000006"/>
</kw>
<kw name="Click Element">
<arg>//android.widget.Button[@resource-id="com.android.permissioncontroller:id/permission_allow_foreground_only_button"]</arg>
<status status="NOT RUN" start="2025-06-12T18:03:24.728215" elapsed="0.000005"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-06-12T18:03:24.728253" elapsed="0.000005"/>
</kw>
<kw name="Capture Page Screenshot">
<status status="NOT RUN" start="2025-06-12T18:03:24.728533" elapsed="0.000005"/>
</kw>
<kw name="Capture Page Screenshot">
<status status="NOT RUN" start="2025-06-12T18:03:24.728803" elapsed="0.000005"/>
</kw>
<kw name="Wait Until Page Contains Element">
<arg>xpath=//android.widget.TextView[@text="Nạp điểm UrBox"]</arg>
<arg>timeout=3s</arg>
<status status="NOT RUN" start="2025-06-12T18:03:24.729040" elapsed="0.000005"/>
</kw>
<kw name="Capture Page Screenshot">
<status status="NOT RUN" start="2025-06-12T18:03:24.729313" elapsed="0.000005"/>
</kw>
<kw name="Click Element">
<arg>xpath=//android.widget.TextView[@text="Nạp điểm UrBox"]</arg>
<status status="NOT RUN" start="2025-06-12T18:03:24.729585" elapsed="0.000005"/>
</kw>
<kw name="Capture Page Screenshot">
<status status="NOT RUN" start="2025-06-12T18:03:24.729862" elapsed="0.000005"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-06-12T18:03:24.729897" elapsed="0.000006"/>
</kw>
<kw name="Capture Page Screenshot">
<status status="NOT RUN" start="2025-06-12T18:03:24.730173" elapsed="0.000005"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${card_digits}</var>
<arg>list('${CARD_CODE}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-12T18:03:24.730213" elapsed="0.000005"/>
</kw>
<for flavor="IN">
<iter>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>'${digit}' == '0'</arg>
<arg>Press Key Code</arg>
<arg>${KEY_CODE_0}</arg>
<arg>ELSE IF</arg>
<arg>'${digit}' == '1'</arg>
<arg>Press Key Code</arg>
<arg>${KEY_CODE_1}</arg>
<arg>ELSE IF</arg>
<arg>'${digit}' == '2'</arg>
<arg>Press Key Code</arg>
<arg>${KEY_CODE_2}</arg>
<arg>ELSE IF</arg>
<arg>'${digit}' == '3'</arg>
<arg>Press Key Code</arg>
<arg>${KEY_CODE_3}</arg>
<arg>ELSE IF</arg>
<arg>'${digit}' == '4'</arg>
<arg>Press Key Code</arg>
<arg>${KEY_CODE_4}</arg>
<arg>ELSE IF</arg>
<arg>'${digit}' == '5'</arg>
<arg>Press Key Code</arg>
<arg>${KEY_CODE_5}</arg>
<arg>ELSE IF</arg>
<arg>'${digit}' == '6'</arg>
<arg>Press Key Code</arg>
<arg>${KEY_CODE_6}</arg>
<arg>ELSE IF</arg>
<arg>'${digit}' == '7'</arg>
<arg>Press Key Code</arg>
<arg>${KEY_CODE_7}</arg>
<arg>ELSE IF</arg>
<arg>'${digit}' == '8'</arg>
<arg>Press Key Code</arg>
<arg>${KEY_CODE_8}</arg>
<arg>ELSE IF</arg>
<arg>'${digit}' == '9'</arg>
<arg>Press Key Code</arg>
<arg>${KEY_CODE_9}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" start="2025-06-12T18:03:24.730342" elapsed="0.000006"/>
</kw>
<var name="${digit}"/>
<status status="NOT RUN" start="2025-06-12T18:03:24.730307" elapsed="0.000081"/>
</iter>
<var>${digit}</var>
<value>@{card_digits}</value>
<status status="NOT RUN" start="2025-06-12T18:03:24.730254" elapsed="0.000232"/>
</for>
<kw name="Capture Page Screenshot">
<status status="NOT RUN" start="2025-06-12T18:03:24.730779" elapsed="0.000005"/>
</kw>
<kw name="Click Element">
<arg>//android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup[2]/android.view.ViewGroup[1]</arg>
<status status="NOT RUN" start="2025-06-12T18:03:24.731053" elapsed="0.000005"/>
</kw>
<kw name="Capture Page Screenshot">
<status status="NOT RUN" start="2025-06-12T18:03:24.731448" elapsed="0.000012"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-06-12T18:03:24.731516" elapsed="0.000008"/>
</kw>
<kw name="Capture Page Screenshot">
<status status="NOT RUN" start="2025-06-12T18:03:24.731829" elapsed="0.000006"/>
</kw>
<status status="FAIL" start="2025-06-12T18:03:24.721153" elapsed="0.010766">No keyword with name 'Open Application' found.</status>
</test>
<status status="FAIL" start="2025-06-12T18:03:24.711389" elapsed="0.020692"/>
</suite>
<statistics>
<total>
<stat pass="0" fail="1" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat pass="0" fail="1" skip="0" id="s1" name="Login">Login</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
