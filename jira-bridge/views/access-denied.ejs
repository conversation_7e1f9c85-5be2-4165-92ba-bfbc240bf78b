<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Access Denied</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
  <style>
    body {
      background-color: #f8f9fa;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      margin: 0;
    }
    .card {
      max-width: 600px;
      width: 100%;
      border: none;
      border-radius: 10px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    .card-header {
      background-color: #dc3545;
      color: white;
      border-radius: 10px 10px 0 0 !important;
      padding: 20px;
    }
    .card-body {
      padding: 30px;
    }
    .icon-container {
      text-align: center;
      margin-bottom: 25px;
    }
    .access-denied-icon {
      font-size: 4rem;
      color: #dc3545;
    }
  </style>
</head>
<body>
  <div class="card text-center">
    <div class="card-header">
      <h2 class="mb-0"><i class="bi bi-shield-lock-fill me-2"></i>Access Denied</h2>
    </div>
    <div class="card-body">
      <div class="icon-container">
        <svg class="access-denied-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
          <path d="M4.285 12.433a.5.5 0 0 0 .683-.183A3.498 3.498 0 0 1 8 10.5c1.295 0 2.426.703 3.032 1.75a.5.5 0 0 0 .866-.5A4.498 4.498 0 0 0 8 9.5a4.5 4.5 0 0 0-3.898 2.25.5.5 0 0 0 .183.683zM7 6.5C7 7.328 6.552 8 6 8s-1-.672-1-1.5S5.448 5 6 5s1 .672 1 1.5zm4 0c0 .828-.448 1.5-1 1.5s-1-.672-1-1.5S9.448 5 10 5s1 .672 1 1.5z"/>
        </svg>
      </div>
      <div class="alert alert-danger">
        <p class="mb-0">Access denied. The URL is invalid or missing required authentication information.</p>
      </div>
      <p class="mt-4">
        This application can only be accessed through a valid link from the main application. Please go back to the main app and use the "Link with Jira" button from there.
      </p>
      <p class="text-muted small mt-4 mb-0">
        If you believe this is an error, please contact your system administrator.
      </p>
    </div>
  </div>
</body>
</html>
