<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Link Jira Account</title>
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
    />
    <link rel="stylesheet" href="/css/styles.css" />
    <style>
      .btn-loading {
        position: relative;
      }
      .btn-loading .spinner {
        display: none;
      }
      .btn-loading.loading .spinner {
        display: inline-block;
      }
      .btn-loading.loading .btn-text {
        opacity: 0;
      }
      .spinner {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 1rem;
        height: 1rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #ffffff;
        animation: spin 1s ease-in-out infinite;
      }
      @keyframes spin {
        to {
          transform: translate(-50%, -50%) rotate(360deg);
        }
      }
    </style>
  </head>
  <body>
    <div class="container mt-5">
      <div class="row justify-content-center">
        <div class="col-md-8">
          <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
              <h2 class="mb-0">Link Jira Account</h2>
            </div>

            <div class="card-body text-center">
              <% if (keycloakVerified) { %>
              <p class="mb-4">
                To complete the process, please link your Jira account.
              </p>

              <div class="d-grid gap-2 col-6 mx-auto">
                <button
                  id="linkJiraBtn"
                  class="btn btn-primary btn-lg btn-loading"
                >
                  <div class="spinner"></div>
                  <span class="btn-text">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      fill="currentColor"
                      class="bi bi-link-45deg me-2"
                      viewBox="0 0 16 16"
                    >
                      <path
                        d="M4.715 6.542 3.343 7.914a3 3 0 1 0 4.243 4.243l1.828-1.829A3 3 0 0 0 8.586 5.5L8 6.086a1.002 1.002 0 0 0-.154.199 2 2 0 0 1 .861 3.337L6.88 11.45a2 2 0 1 1-2.83-2.83l.793-.792a4.018 4.018 0 0 1-.128-1.287z"
                      />
                      <path
                        d="M6.586 4.672A3 3 0 0 0 7.414 9.5l.775-.776a2 2 0 0 1-.896-3.346L9.12 3.55a2 2 0 1 1 2.83 2.83l-.793.792c.112.42.155.855.128 1.287l1.372-1.372a3 3 0 1 0-4.243-4.243L6.586 4.672z"
                      />
                    </svg>
                    Link with Jira
                  </span>
                </button>
              </div>

              <% } else { %>
              <div class="alert alert-danger mb-4">
                <p class="mb-0">
                  <i class="bi bi-exclamation-triangle-fill me-2"></i>
                  Authentication failed. Please try again or contact the
                  administrator.
                </p>
              </div>
              <% } %>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const linkJiraBtn = document.getElementById("linkJiraBtn");

        if (linkJiraBtn) {
          linkJiraBtn.addEventListener("click", function (e) {
            e.preventDefault();

            this.disabled = true;
            this.classList.add("loading");

            setTimeout(() => {
              window.location.href = "/auth/jira";
            }, 100);
          });
        }
      });
    </script>
  </body>
</html>
