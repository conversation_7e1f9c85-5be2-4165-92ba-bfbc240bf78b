{"name": "jira-bridge", "version": "1.0.0", "description": "Node.js app for Jira OAuth and remote link management", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["jira", "o<PERSON>h", "express", "remote-link"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.5", "cookie-parser": "^1.4.6", "dotenv": "^16.3.1", "express": "4.21.2", "express-session": "^1.17.3", "ejs": "^3.1.9", "jwks-rsa": "^3.1.0", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "pg-promise": "^11.5.4", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}}