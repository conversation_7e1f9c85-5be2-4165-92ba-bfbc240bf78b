"use client";

import { createContext, useContext, useState, useCallback } from "react";

const BreadcrumbContext = createContext();

export const BreadcrumbProvider = ({ children }) => {
  const [breadcrumbItems, setBreadcrumbItems] = useState([]);

  const updateBreadcrumb = useCallback((items) => {
    setBreadcrumbItems(items);
  }, []);

  const resetBreadcrumb = useCallback(() => {
    setBreadcrumbItems([]);
  }, []);

  return (
    <BreadcrumbContext.Provider
      value={{
        breadcrumbItems,
        updateBreadcrumb,
        resetBreadcrumb,
      }}
    >
      {children}
    </BreadcrumbContext.Provider>
  );
};

export const useBreadcrumb = () => {
  const context = useContext(BreadcrumbContext);
  if (!context) {
    throw new Error("useBreadcrumb must be used within a BreadcrumbProvider");
  }
  return context;
};
