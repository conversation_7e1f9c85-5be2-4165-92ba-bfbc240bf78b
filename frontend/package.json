{"name": "urdraw-workspace", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@monaco-editor/react": "4.7.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/forms": "^0.5.7", "@tanstack/react-query": "^5.71.1", "@tanstack/react-table": "^8.21.3", "axios": "^1.6.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "dayjs": "^1.11.13", "keycloak-js": "26.1.4", "lodash": "^4.17.21", "lucide-react": "^0.485.0", "monaco-editor": "0.52.2", "next": "14.0.3", "next-themes": "^0.4.6", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.2", "react-markdown": "^10.1.0", "react-query": "^3.39.3", "recharts": "^2.15.1", "sonner": "^2.0.2", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.3", "postcss": "^8", "tailwindcss": "^3.3.0"}}