"use client";

import { useState, useEffect, useCallback } from "react";
import { Controller } from "react-hook-form";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import {
  LoaderCircle,
  FileText,
  Tag,
  Clock,
  User,
  Calendar,
  Zap,
  Target,
  Hash,
  Plus,
  Bug,
} from "lucide-react";
import TagInput from "@/components/TagInput";
import { useForm } from "react-hook-form";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { ManualTest<PERSON><PERSON>, ProjectApi } from "@/lib/api";
import { PROJECT_DETAIL_QUERY_KEY } from "@/hooks/useProjects";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import CreateBugModal from "@/components/manual-test/CreateBugModal";
import BugList from "@/components/manual-test/BugList";
import { useBreadcrumb } from "@/contexts/BreadcrumbContext";

dayjs.extend(utc);
dayjs.extend(timezone);

const priorityOptions = [
  {
    value: "High",
    label: "High",
    unselectedColor:
      "bg-gray-50 hover:bg-red-100 text-red-500 border-gray-200 " +
      "dark:bg-red-900 dark:text-red-300 dark:border-red-700 " +
      "dark:hover:bg-red-800 dark:hover:text-red-100 dark:hover:border-red-600",
    selectedColor:
      "bg-red-300 hover:bg-red-400 text-white border-red-300 " +
      "dark:bg-red-500 dark:hover:bg-red-600 dark:text-white dark:border-red-500 shadow-sm ring-2 ring-red-50 dark:ring-red-400",
    icon: "🔴",
  },
  {
    value: "Medium",
    label: "Medium",
    unselectedColor:
      "bg-gray-50 hover:bg-amber-100 text-amber-500 border-gray-200 " +
      "dark:bg-amber-900 dark:text-amber-300 dark:border-amber-700 " +
      "dark:hover:bg-amber-800 dark:hover:text-amber-100 dark:hover:border-amber-600",
    selectedColor:
      "bg-amber-300 hover:bg-amber-400 text-white border-amber-300 " +
      "dark:bg-amber-500 dark:hover:bg-amber-600 dark:text-white dark:border-amber-500 shadow-sm ring-2 ring-amber-50 dark:ring-amber-400",
    icon: "🟡",
  },
  {
    value: "Low",
    label: "Low",
    unselectedColor:
      "bg-gray-50 hover:bg-green-100 text-green-500 border-gray-200 " +
      "dark:bg-green-900 dark:text-green-300 dark:border-green-700 " +
      "dark:hover:bg-green-800 dark:hover:text-green-100 dark:hover:border-green-600",
    selectedColor:
      "bg-green-300 hover:bg-green-400 text-white border-green-300 " +
      "dark:bg-green-500 dark:hover:bg-green-600 dark:text-white dark:border-green-500 shadow-sm ring-2 ring-green-50 dark:ring-green-400",
    icon: "🟢",
  },
];

const categoryOptions = [
  { value: "functional", label: "Functional Test", icon: "⚙️" },
  { value: "ui", label: "UI Test", icon: "🎨" },
  { value: "integration", label: "Integration Test", icon: "🔗" },
  { value: "api", label: "API Test", icon: "🌐" },
  { value: "performance", label: "Performance Test", icon: "🚀" },
  { value: "security", label: "Security Test", icon: "🔒" },
];

export default function ManualTestCaseEditor() {
  const searchParams = useSearchParams();
  const projectId = searchParams.get("projectId");
  const testCaseId = searchParams.get("testCaseId");
  const projectName = decodeURIComponent(searchParams.get("project") || "");
  const router = useRouter();
  const queryClient = useQueryClient();
  const { updateBreadcrumb, resetBreadcrumb } = useBreadcrumb();

  const [tags, setTags] = useState([]);
  const [priority, setPriority] = useState("Medium");
  const [lastSaved, setLastSaved] = useState(null);
  const [currentStatus, setCurrentStatus] = useState("new");
  const [isCreateBugModalOpen, setIsCreateBugModalOpen] = useState(false);

  const isEditMode = !!testCaseId;

  const {
    register,
    handleSubmit,
    getValues,
    setValue,
    watch,
    reset,
    control,
    formState: { errors, isSubmitting: isTestCaseFormSubmitting },
  } = useForm({
    defaultValues: {
      name: "",
      category: "",
      estimatedTime: "",
      assignedTo: "",
      dueDate: "",
      status: "new",
      inputDescription: "",
      actualResult: "",
      desiredResults: "",
    },
  });

  const {
    data: testCaseDetail,
    isLoading: testCaseLoading,
    isSuccess: testCaseFetched,
  } = useQuery({
    queryKey: ["manual-test-case", testCaseId],
    queryFn: () => ManualTestApi().getTestCase(testCaseId),
    enabled: isEditMode,
  });

  const {
    data: staffQueryData,
    isLoading: staffLoading,
    isSuccess: staffFetched,
  } = useQuery({
    queryKey: ["available-staff", projectId],
    queryFn: () => ProjectApi().getAvailableStaff(projectId),
    enabled: !!projectId,
    select: (response) => response.availableStaff,
  });
  const availableStaff = staffQueryData || [];

  useEffect(() => {
    const breadcrumbItems = [];

    breadcrumbItems.push({
      label: "Manual Test",
      href: `/manual-test?projectId=${projectId}`,
      clickable: true,
      active: false,
    });

    if (projectId && projectName) {
      breadcrumbItems.push({
        label: projectName,
        href: `/manual-test?projectId=${projectId}`,
        clickable: true,
        active: false,
      });
    }

    breadcrumbItems.push({
      label: testCaseId ? "Edit Test Case" : "Create Test Case",
      href: null,
      clickable: false,
      active: true,
    });

    updateBreadcrumb(breadcrumbItems);

    return () => {
      resetBreadcrumb();
    };
  }, [projectId, projectName, testCaseId, updateBreadcrumb, resetBreadcrumb]);

  const resetFormWithData = useCallback(
    (data) => {
      const defaultValues = {
        name: data?.name || "",
        category: data?.category || "",
        estimatedTime: data?.estimatedTime?.toString() || "",
        assignedTo: data?.assignedTo || "",
        dueDate: data?.dueDate ? dayjs(data.dueDate).format("YYYY-MM-DD") : "",
        priority: data?.priority || "Medium",
        status: data?.status || "new",
      };
      reset(defaultValues);
      setValue("category", defaultValues.category);
      if (availableStaff && availableStaff.length > 0) {
        const staffExists = availableStaff.some(
          (s) => s.email === defaultValues.assignedTo
        );
        if (staffExists) {
          setValue("assignedTo", defaultValues.assignedTo);
        } else {
          setValue("assignedTo", "");
        }
      } else if (!data?.assignedToEmail) {
        setValue("assignedTo", "");
      }
      setValue("dataTest", data?.dataTest || "");
      setValue("inputDescription", data?.inputDescription || "");
      setValue("desiredResults", data?.desiredResults || "");
      setPriority(data?.priority || "Medium");
      setLastSaved(data?.updatedAt || data?.createdAt || null);
    },
    [reset, setValue, availableStaff, testCaseDetail]
  );

  useEffect(() => {
    if (isEditMode && testCaseFetched && testCaseDetail) {
      if (staffFetched) {
        resetFormWithData(testCaseDetail);
      }
    } else if (!isEditMode) {
      resetFormWithData(null);
    }
  }, [
    isEditMode,
    testCaseDetail,
    testCaseFetched,
    staffFetched,
    resetFormWithData,
  ]);

  const testCaseMutation = useMutation({
    mutationFn: (payload) => {
      const bugUpdatedFlag = localStorage.getItem("bug_updated_trigger");
      if (bugUpdatedFlag) {
        return Promise.resolve({ skippedDueToBugUpdate: true });
      }
      if (isEditMode) {
        return ManualTestApi().updateTestCase(testCaseId, payload.data);
      } else {
        return ManualTestApi().createTestCase(payload.data);
      }
    },
    onSuccess: async (data, variables) => {
      if (data && data.skippedDueToBugUpdate) {
        localStorage.removeItem("bug_updated_trigger");
        await queryClient.invalidateQueries({
          queryKey: ["manual-test-case", testCaseId],
        });
        await queryClient.invalidateQueries({
          queryKey: ["available-staff", projectId],
        });
        await queryClient.invalidateQueries({
          queryKey: [PROJECT_DETAIL_QUERY_KEY, projectId],
        });
        await queryClient.invalidateQueries({
          queryKey: ["manual-test-cases", projectId],
        });
        localStorage.setItem("manual_test_updated", "true");
        return;
      }

      const bugUpdatedFlag = localStorage.getItem("bug_updated_trigger");
      if (bugUpdatedFlag) {
        localStorage.removeItem("bug_updated_trigger");
        await queryClient.invalidateQueries({
          queryKey: ["manual-test-case", testCaseId],
        });
        await queryClient.invalidateQueries({
          queryKey: ["available-staff", projectId],
        });
        await queryClient.invalidateQueries({
          queryKey: [PROJECT_DETAIL_QUERY_KEY, projectId],
        });
        await queryClient.invalidateQueries({
          queryKey: ["manual-test-cases", projectId],
        });
        localStorage.setItem("manual_test_updated", "true");
        return;
      }

      if (variables.action === "create") {
        toast.success("Test case created successfully");
      } else if (variables.action === "update") {
        toast.success("Test case updated successfully");
      }

      if (isEditMode) {
        await queryClient.invalidateQueries({
          queryKey: ["manual-test-case", testCaseId],
        });
      }
      await queryClient.invalidateQueries({
        queryKey: ["available-staff", projectId],
      });
      await queryClient.invalidateQueries({
        queryKey: [PROJECT_DETAIL_QUERY_KEY, projectId],
      });
      await queryClient.invalidateQueries({
        queryKey: ["manual-test-cases", projectId],
      });

      localStorage.setItem("manual_test_updated", "true");

      if (data && data.id && variables.action === "create") {
        setLastSaved(data.createdAt);
        setCurrentStatus(data.status);
        if (!variables.isSaveAndAddAnother) {
          router.replace(
            `/manual-test/ur-editor?project=${encodeURIComponent(
              searchParams.get("project") || ""
            )}&projectId=${projectId}&testCaseId=${data.id}`
          );
        }
      } else if (data && variables.action === "update") {
        setLastSaved(data.updatedAt || data.createdAt);
        setCurrentStatus(data.status);
      }

      if (variables.redirectAfterSave && variables.action === "update") {
        router.push(`/manual-test?projectId=${projectId}`);
      }
    },
    onError: (error) => {
      console.error("Error saving test case:", error);
      toast.error(error.response?.data?.message || "Failed to save test case");
    },
  });

  const processSaveTestCase = async (
    formData,
    statusOverride = null,
    redirectAfterSave = true,
    shouldResetForm = false
  ) => {
    const estTime = formData.estimatedTime;
    const estimatedTimeValue =
      estTime && !isNaN(parseInt(estTime, 10))
        ? parseInt(estTime, 10)
        : undefined;

    const apiPayload = {
      ...formData,
      priority,
      tags,
      projectId,
      estimatedTime: estimatedTimeValue,
      dueDate: formData.dueDate
        ? dayjs(formData.dueDate).endOf("day").toISOString()
        : null,
    };

    const actionType = isEditMode ? "update" : "create";

    await testCaseMutation.mutateAsync({
      data: apiPayload,
      action: actionType,
      isSaveAndAddAnother: shouldResetForm,
      redirectAfterSave: redirectAfterSave,
    });

    if (shouldResetForm) {
      resetFormWithData(null);
      toast.success("Ready to create another test case");
    }
  };

  const onValidSubmitTestCase = (data) => {
    processSaveTestCase(data, null, isEditMode, false);
  };

  const handleCancel = () => {
    router.push(`/manual-test?projectId=${projectId}`);
  };

  const handleSaveAndAddAnother = async () => {
    const data = getValues();
    let valid = true;
    if (!data.name?.trim()) {
      toast.error("Test case name is required");
      valid = false;
    }
    if (!data.category) {
      toast.error("Category is required");
      valid = false;
    }
    if (!valid) return;

    processSaveTestCase(data, null, false, true);
  };

  const handleBugCreated = () => {
    queryClient.invalidateQueries(["bugs-for-test-case", testCaseId]);
  };

  const openCreateBugModal = (e) => {
    setIsCreateBugModalOpen(true);
  };

  if (
    (isEditMode && testCaseLoading) ||
    (!isEditMode && staffLoading && !staffFetched) ||
    (isEditMode && !testCaseFetched && testCaseLoading)
  ) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoaderCircle className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  const categorySelectKey = `category-${testCaseId || "new"}-${watch(
    "category"
  )}`;
  const assignedToSelectKey = `assignedTo-${testCaseId || "new"}-${watch(
    "assignedTo"
  )}-${availableStaff.length}`;
  const status = watch("status") || "new";

  const statusBorderColors = {
    new: "border-blue-500",
    passed: "border-green-500",
    failed: "border-red-500",
  };

  return (
    <>
      <form onSubmit={handleSubmit(onValidSubmitTestCase)} className="w-full">
        <div
          className={`grid grid-cols-1 ${
            isEditMode ? "lg:grid-cols-2" : "lg:grid-cols-1"
          } gap-8 p-8`}
        >
          <Card className="shadow-2xl border border-slate-200 dark:border-slate-700 bg-card backdrop-blur-sm">
            <CardHeader className="pb-4">
              <div className="flex gap-4 flex-wrap items-start">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                  <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>

                <div className="flex flex-col flex-1 ">
                  <Input
                    id="name"
                    {...register("name", {
                      required: "Test case name is required",
                    })}
                    placeholder="Test Case Name"
                    className="h-10 text-sm font-semibold placeholder:text-gray-500"
                  />
                  {errors.name && (
                    <p className="text-xs text-red-500 mt-1">
                      {errors.name.message}
                    </p>
                  )}
                </div>

                <div className="flex flex-col w-[110px]">
                  <Controller
                    name="status"
                    control={control}
                    defaultValue="new"
                    render={({ field }) => (
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={!isEditMode}
                      >
                        <SelectTrigger
                          className={`h-10 text-sm rounded-md px-3 bg-white border ${
                            statusBorderColors[field.value || "new"]
                          } `}
                        >
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="rounded-md bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700">
                          <SelectItem value="new">
                            <span className="flex items-center gap-2 text-blue-600 dark:text-blue-400">
                              <span className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full" />
                              New
                            </span>
                          </SelectItem>
                          <SelectItem value="passed">
                            <span className="flex items-center gap-2 text-green-600 dark:text-green-400">
                              <span className="w-2 h-2 bg-green-600 dark:bg-green-400 rounded-full" />
                              Passed
                            </span>
                          </SelectItem>
                          <SelectItem value="failed">
                            <span className="flex items-center gap-2 text-red-600 dark:text-red-400">
                              <span className="w-2 h-2 bg-red-600 dark:bg-red-400 rounded-full" />
                              Failed
                            </span>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div
                className={`grid grid-cols-1 ${
                  isEditMode ? "lg:grid-cols-1" : "lg:grid-cols-2"
                } gap-8`}
              >
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label
                      htmlFor="testData"
                      className="text-sm font-medium flex items-center gap-2"
                    >
                      <FileText className="h-4 w-4 text-slate-500 dark:text-slate-400" />
                      Test Data *
                    </Label>
                    <Textarea
                      id="dataTest"
                      rows={4}
                      className="resize-y min-h-[6rem]"
                      {...register("dataTest")}
                    />
                    {errors.testData && (
                      <p className="text-xs text-red-500 mt-1">
                        {errors.testData.message}
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label
                      htmlFor="inputDescription"
                      className="text-sm font-medium"
                    >
                      Input
                    </Label>
                    <Textarea
                      id="inputDescription"
                      {...register("inputDescription")}
                      className="resize-y min-h-[6rem]"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label
                      htmlFor="desiredResults"
                      className="text-sm font-medium"
                    >
                      Expected Result
                    </Label>
                    <Textarea
                      id="desiredResults"
                      {...register("desiredResults")}
                      className="resize-y min-h-[6rem]"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label
                      htmlFor="category"
                      className="text-sm font-medium flex items-center gap-2"
                    >
                      <Target className="h-4 w-4 text-slate-500 dark:text-slate-400" />
                      Category *
                    </Label>
                    <Select
                      key={categorySelectKey}
                      value={watch("category") || categoryOptions[0]?.value}
                      onValueChange={(value) =>
                        setValue("category", value, { shouldValidate: true })
                      }
                    >
                      <SelectTrigger className="h-11">
                        <SelectValue placeholder="Select test category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categoryOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            <div className="flex items-center gap-2">
                              <span>{option.icon}</span>
                              {option.label}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.category && (
                      <p className="text-xs text-red-500 mt-1">
                        {errors.category.message}
                      </p>
                    )}
                  </div>

                  <div className="flex gap-4">
                    <div className="flex-1 space-y-3">
                      <Label
                        htmlFor="priority"
                        className="text-sm font-medium flex items-center gap-2"
                      >
                        <Zap className="h-4 w-4 text-slate-500 dark:text-slate-400" />
                        Priority *
                      </Label>
                      <Select
                        value={priority}
                        onValueChange={(value) => setPriority(value)}
                      >
                        <SelectTrigger id="priority" className="h-11">
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                        <SelectContent>
                          {priorityOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              <div className="flex items-center gap-2">
                                {option.icon}
                                {option.label}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex-1 space-y-3">
                      <Label
                        htmlFor="assignedTo"
                        className="text-sm font-medium"
                      >
                        Assigned To
                      </Label>
                      <Select
                        key={assignedToSelectKey}
                        value={watch("assignedTo") || availableStaff[0]?.email}
                        onValueChange={(value) => setValue("assignedTo", value)}
                      >
                        <SelectTrigger className="h-11 truncate text-left">
                          <SelectValue placeholder="Select assignee" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableStaff.map((user) => (
                            <SelectItem key={user.email} value={user.email}>
                              <div className="flex items-center gap-3">
                                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-semibold">
                                  {(user.username || user.email)
                                    .substring(0, 2)
                                    .toUpperCase()}
                                </div>
                                <div className="flex flex-col">
                                  <span className="font-medium">
                                    {user.username || user.email.split("@")[0]}
                                  </span>
                                  <span className="text-xs text-slate-500 dark:text-slate-400">
                                    {user.email}
                                  </span>
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex gap-4 mt-4">
                    <div className="flex-1 space-y-2">
                      <Label
                        htmlFor="estimatedTime"
                        className="text-sm font-medium flex items-center gap-2"
                      >
                        <Clock className="h-4 w-4 text-slate-500 dark:text-slate-400" />
                        Estimated Time (minutes)
                      </Label>
                      <Input
                        id="estimatedTime"
                        type="number"
                        {...register("estimatedTime", {
                          min: {
                            value: 0,
                            message: "Estimated time cannot be negative",
                          },
                        })}
                        placeholder="15"
                        className="h-11"
                      />
                      {errors.estimatedTime && (
                        <p className="text-xs text-red-500 mt-1">
                          {errors.estimatedTime.message}
                        </p>
                      )}
                    </div>

                    <div className="flex-1 space-y-2">
                      <Label
                        htmlFor="dueDate"
                        className="text-sm font-medium flex items-center gap-2"
                      >
                        <Calendar className="h-4 w-4 text-slate-500 dark:text-slate-400" />
                        Due Date
                      </Label>
                      <Input
                        id="dueDate"
                        type="date"
                        {...register("dueDate")}
                        defaultValue={
                          new Date(Date.now() + 86400000)
                            .toISOString()
                            .split("T")[0]
                        }
                        className="h-11"
                      />
                    </div>
                  </div>
                  <div className="flex gap-2 pt-6">
                    {!isEditMode && (
                      <Button
                        type="button"
                        onClick={handleSaveAndAddAnother}
                        disabled={isTestCaseFormSubmitting}
                        variant="outline"
                        className="flex-1 border-dashed border-2 border-green-300 text-green-700 hover:bg-green-50"
                      >
                        <Plus className="mr-2 h-4 w-4" /> Create and Add Another
                        Test Case
                      </Button>
                    )}
                    <Button
                      type="submit"
                      disabled={isTestCaseFormSubmitting}
                      className="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white"
                    >
                      {isTestCaseFormSubmitting && (
                        <LoaderCircle className="animate-spin mr-2 h-4 w-4" />
                      )}
                      {isEditMode ? "Update Test Case" : "Create Test Case"}
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {isEditMode && (
            <Card className="shadow-2xl border border-slate-200 dark:border-slate-700 bg-card backdrop-blur-sm mt-0">
              <CardHeader className="pb-4 flex flex-row items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-red-100 dark:bg-red-900/30 rounded-lg">
                    <Bug className="h-5 w-5 text-red-600 dark:text-red-400" />
                  </div>
                  <CardTitle className="text-lg">Bug Management</CardTitle>
                </div>
                <Button
                  type="button"
                  size="sm"
                  variant="outline"
                  onClick={openCreateBugModal}
                >
                  <Plus className="h-4 w-4" /> Add New Bug
                </Button>
              </CardHeader>
              <CardContent>
                <BugList testCaseId={testCaseId} />
              </CardContent>
            </Card>
          )}
        </div>
      </form>

      {isEditMode && (
        <CreateBugModal
          open={isCreateBugModalOpen}
          setOpen={setIsCreateBugModalOpen}
          testCaseId={testCaseId}
          projectId={projectId}
          onBugCreated={handleBugCreated}
        />
      )}
      <CreateBugModal
        open={isCreateBugModalOpen}
        setOpen={setIsCreateBugModalOpen}
        testCaseId={testCaseId}
        projectId={projectId}
        onBugCreated={handleBugCreated}
      />
    </>
  );
}
