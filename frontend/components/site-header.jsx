import { ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useBreadcrumb } from "@/contexts/BreadcrumbContext";
import { useRouter, usePathname } from "next/navigation";

export function SiteHeader() {
  const { breadcrumbItems } = useBreadcrumb();
  const router = useRouter();
  const pathname = usePathname();

  const handleBreadcrumbClick = (item) => {
    if (item.clickable && item.href && !item.active) {
      if (item.action === "fullCode" && window.handleBreadcrumbItemClick) {
        window.handleBreadcrumbItemClick(item);
      } else if (item.action === "navigate") {
        router.push(item.href);
      } else {
        router.push(item.href);
      }
    }
  };

  const shouldShowBreadcrumb = breadcrumbItems.length > 0;

  return (
    <header className="flex h-14 items-center gap-4 border-b bg-background px-6">
      <div className="flex items-center space-x-1 text-sm text-muted-foreground">
        {shouldShowBreadcrumb
          ? breadcrumbItems.map((item, index) => (
              <div key={index} className="flex items-center">
                {index > 0 && (
                  <ChevronRight className="h-4 w-4 mx-2 text-muted-foreground/60" />
                )}
                {item.clickable && item.href && !item.active ? (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-auto p-1 text-sm font-normal text-blue-600 hover:text-blue-800 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/20"
                    onClick={() => handleBreadcrumbClick(item)}
                  >
                    {item.label}
                  </Button>
                ) : (
                  <span
                    className={`text-sm ${
                      item.active
                        ? "text-foreground font-medium"
                        : "text-blue-600 dark:text-blue-400"
                    }`}
                  >
                    {item.label}
                  </span>
                )}
              </div>
            ))
          : null}
      </div>

      <div className="ml-auto flex items-center space-x-4">
        <div id="page-header-controls" />
      </div>
    </header>
  );
}
