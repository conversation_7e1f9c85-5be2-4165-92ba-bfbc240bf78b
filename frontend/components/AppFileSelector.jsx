import React, { useState, useEffect } from "react";
import {
  Upload,
  Smartphone,
  CheckCircle,
  AlertCircle,
  RefreshCw,
} from "lucide-react";
import axios from "axios";
import { getToken } from "@/lib/keycloak";

const AppFileSelector = ({
  onAppSelected,
  selectedApp,
}) => {
  const [mode, setMode] = useState("select");
  const [uploadedApps, setUploadedApps] = useState([]);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState(null);

  // Upload form state
  const [uploadForm, setUploadForm] = useState({
    filePath: "",
    platform: "android",
    name: "",
  });

  // Load uploaded apps
  const loadUploadedApps = async () => {
    setLoading(true);
    setError(null);

    try {
      const token = getToken();
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/api/device-farm/apps`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );
      const data = response.data;

      if (data.success) {
        setUploadedApps(data.apps);
      } else {
        setError(data.message || "Failed to load apps");
      }
    } catch (err) {
      setError("Failed to connect to server");
      console.error("Error loading apps:", err);
    } finally {
      setLoading(false);
    }
  };

  // Upload new app
  const handleUpload = async () => {
    if (!uploadForm.filePath || !uploadForm.name) {
      setError("File path and app name are required");
      return;
    }

    setUploading(true);
    setError(null);

    try {
      const response = await fetch("/api/device-farm/apps/upload", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(uploadForm),
      });

      const data = await response.json();

      if (data.success) {
        // Add new app to list
        setUploadedApps((prev) => [data.app, ...prev]);

        // Auto-select the uploaded app
        onAppSelected({
          arn: data.appArn,
          name: uploadForm.name,
          platform: uploadForm.platform,
        });

        // Reset form
        setUploadForm({
          filePath: "",
          platform: "android",
          name: "",
        });

        // Switch to select mode to show the uploaded app
        setMode("select");
      } else {
        setError(data.message || "Upload failed");
      }
    } catch (err) {
      setError("Failed to upload app");
      console.error("Error uploading app:", err);
    } finally {
      setUploading(false);
    }
  };

  // Select existing app
  const handleSelectApp = (app) => {
    onAppSelected({
      arn: app.arn,
      name: app.name,
      platform: app.platform,
    });
  };

  // Load apps on component mount
  useEffect(() => {
    if (mode === "select") {
      loadUploadedApps();
    }
  }, [mode]);

  const getStatusIcon = (status) => {
    switch (status) {
      case "SUCCEEDED":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "FAILED":
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case "PROCESSING":
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getPlatformIcon = (platform) => {
    return (
      <Smartphone
        className={`w-4 h-4 ${
          platform === "android" ? "text-green-600" : "text-gray-600"
        }`}
      />
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex space-x-2">
        <button
          onClick={() => setMode("select")}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            mode === "select"
              ? "bg-blue-600 text-white"
              : "bg-gray-200 text-gray-700 hover:bg-gray-300"
          }`}
        >
          Select Existing App
        </button>
        <button
          onClick={() => setMode("upload")}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            mode === "upload"
              ? "bg-blue-600 text-black"
              : "bg-gray-200 text-black-700 hover:bg-gray-300"
          }`}
        >
          Upload New App
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <div className="flex">
            <AlertCircle className="w-5 h-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {mode === "upload" && (
        <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-4">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <Upload className="w-5 h-5 mr-2" />
            Upload New App
          </h3>

          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                App Name
              </label>
              <input
                type="text"
                value={uploadForm.name}
                onChange={(e) =>
                  setUploadForm((prev) => ({ ...prev, name: e.target.value }))
                }
                placeholder="Enter app name"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Platform
              </label>
              <select
                value={uploadForm.platform}
                onChange={(e) =>
                  setUploadForm((prev) => ({
                    ...prev,
                    platform: e.target.value,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="android">Android (.apk)</option>
                <option value="ios">iOS (.ipa)</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                File Path
              </label>
              <input
                type="text"
                value={uploadForm.filePath}
                onChange={(e) =>
                  setUploadForm((prev) => ({
                    ...prev,
                    filePath: e.target.value,
                  }))
                }
                placeholder="/path/to/your/app.apk"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">
                Enter the full path to your{" "}
                {uploadForm.platform === "android" ? "APK" : "IPA"} file
              </p>
            </div>

            <button
              onClick={handleUpload}
              disabled={uploading || !uploadForm.filePath || !uploadForm.name}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {uploading ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="w-4 h-4 mr-2" />
                  Upload App
                </>
              )}
            </button>
          </div>
        </div>
      )}

      {mode === "select" && (
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Smartphone className="w-5 h-5 mr-2" />
              Select Existing App
            </h3>
            <button
              onClick={loadUploadedApps}
              disabled={loading}
              className="text-blue-600 hover:text-blue-800 flex items-center text-sm"
            >
              <RefreshCw
                className={`w-4 h-4 mr-1 ${loading ? "animate-spin" : ""}`}
              />
              Refresh
            </button>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="w-6 h-6 animate-spin text-blue-500" />
              <span className="ml-2 text-gray-600">Loading apps...</span>
            </div>
          ) : uploadedApps.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Smartphone className="w-12 h-12 mx-auto mb-2 text-gray-300" />
              <p>No apps uploaded yet</p>
              <p className="text-sm">Upload your first app to get started</p>
            </div>
          ) : (
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {uploadedApps.map((app) => (
                <div
                  key={app.arn}
                  onClick={() => handleSelectApp(app)}
                  className={`p-3 border rounded-md cursor-pointer transition-colors ${
                    selectedApp?.arn === app.arn
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getPlatformIcon(app.platform)}
                      <div>
                        <p className="font-medium text-gray-900">{app.name}</p>
                        <p className="text-sm text-gray-500">
                          {app.platform.toUpperCase()} •{" "}
                          {new Date(app.created).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(app.status)}
                      <span className="text-xs text-gray-500">
                        {app.status}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {selectedApp && (
        <div className="bg-green-50 border border-green-200 rounded-md p-3">
          <div className="flex items-center">
            <CheckCircle className="w-5 h-5 text-green-400" />
            <div className="ml-3">
              <p className="text-sm text-green-800">
                Selected: <strong>{selectedApp.name}</strong> (
                {selectedApp.platform.toUpperCase()})
              </p>
              {selectedApp.arn && (
                <p className="text-xs text-green-600 mt-1">
                  ARN: {selectedApp.arn}
                </p>
              )}
              {selectedApp.path && (
                <p className="text-xs text-green-600 mt-1">
                  Path: {selectedApp.path}
                </p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AppFileSelector;
