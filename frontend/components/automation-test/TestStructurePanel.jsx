import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { Plus, LoaderCircle } from "lucide-react";
import { toast } from "sonner";

export default function TestStructurePanel({
  parsedSections,
  activeSection,
  setActiveSection,
  onAddTestCase,
}) {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [newTestCaseName, setNewTestCaseName] = useState("");
  const [newTestCaseDocumentation, setNewTestCaseDocumentation] = useState("");
  const [isCreating, setIsCreating] = useState(false);

  const handleSectionClick = (sectionKey) => {
    setActiveSection(sectionKey);
  };

  const isActive = (sectionKey) => activeSection === sectionKey;

  const handleCreateTestCase = async () => {
    if (!newTestCaseName.trim()) {
      toast.error("Test case name is required");
      return;
    }

    setIsCreating(true);

    try {
      const documentation =
        newTestCaseDocumentation.trim() ||
        `Test case for ${newTestCaseName.trim()}`;

      const newTestCase = {
        name: newTestCaseName.trim(),
        content: `    [Documentation]    ${documentation}`,
      };

      if (onAddTestCase) {
        onAddTestCase(newTestCase);
      }

      setNewTestCaseName("");
      setNewTestCaseDocumentation("");
      setIsCreateModalOpen(false);
      toast.success("Test case created successfully");

      const newTestCaseIndex = parsedSections.testCases.length;
      setActiveSection(`TestCase_${newTestCaseIndex}`);
    } catch (error) {
      toast.error("Failed to create test case");
    } finally {
      setIsCreating(false);
    }
  };

  const handleModalClose = () => {
    setIsCreateModalOpen(false);
    setNewTestCaseName("");
    setNewTestCaseDocumentation("");
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleCreateTestCase();
    }
  };

  return (
    <Card className="flex flex-col border rounded-lg bg-card overflow-hidden h-full">
      <CardContent className="flex-1 p-0 overflow-hidden flex flex-col">
        <div className="flex flex-col h-full">
          <div className="flex-shrink-0 p-2 space-y-1">
            <button
              className={cn(
                "flex items-center w-full px-3 py-1.5 text-sm rounded-md transition-colors",
                isActive("FullCode")
                  ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 font-medium"
                  : "hover:bg-muted-foreground/10 text-muted-foreground"
              )}
              onClick={() => handleSectionClick("FullCode")}
            >
              Full Code
            </button>

            <div className="border-t border-border my-2"></div>

            {parsedSections.settings.trim() !== "" && (
              <button
                className={cn(
                  "flex items-center w-full px-3 py-1.5 text-sm rounded-md transition-colors",
                  isActive("Settings")
                    ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 font-medium"
                    : "hover:bg-muted-foreground/10 text-muted-foreground"
                )}
                onClick={() => handleSectionClick("Settings")}
              >
                Settings
              </button>
            )}

            {parsedSections.variables.trim() !== "" && (
              <button
                className={cn(
                  "flex items-center w-full px-3 py-1.5 text-sm rounded-md transition-colors",
                  isActive("Variables")
                    ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 font-medium"
                    : "hover:bg-muted-foreground/10 text-muted-foreground"
                )}
                onClick={() => handleSectionClick("Variables")}
              >
                Variables
              </button>
            )}

            {parsedSections.keywords.trim() !== "" && (
              <button
                className={cn(
                  "flex items-center w-full px-3 py-1.5 text-sm rounded-md transition-colors",
                  isActive("Keywords")
                    ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 font-medium"
                    : "hover:bg-muted-foreground/10 text-muted-foreground"
                )}
                onClick={() => handleSectionClick("Keywords")}
              >
                Keywords
              </button>
            )}

            {parsedSections.tasks.trim() !== "" && (
              <button
                className={cn(
                  "flex items-center w-full px-3 py-1.5 text-sm rounded-md transition-colors",
                  isActive("Tasks")
                    ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 font-medium"
                    : "hover:bg-muted-foreground/10 text-muted-foreground"
                )}
                onClick={() => handleSectionClick("Tasks")}
              >
                Tasks
              </button>
            )}
          </div>

          {parsedSections.testCases.length > 0 && (
            <>
              <div className="border-t border-border mx-2"></div>

              <div className="flex-shrink-0 px-2 py-2">
                <div className="flex items-center justify-between px-3 py-1 bg-muted/50 rounded-md border">
                  <span className="text-xs font-bold text-foreground uppercase tracking-wide">
                    Test Cases ({parsedSections.testCases.length})
                  </span>
                  <Button
                    onClick={() => setIsCreateModalOpen(true)}
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-primary/10 text-primary hover:text-primary"
                    title="Create Test Case"
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
              </div>

              <div className="flex-1 overflow-hidden">
                <ScrollArea className="h-full">
                  <div className="px-2 pb-2 space-y-1">
                    {parsedSections.testCases.map((testCase, index) => (
                      <button
                        key={`TestCase_${index}`}
                        className={cn(
                          "flex items-center w-full px-3 py-1.5 text-sm rounded-md transition-colors text-left group",
                          isActive(`TestCase_${index}`)
                            ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 font-medium"
                            : "hover:bg-muted-foreground/10 text-muted-foreground"
                        )}
                        onClick={() => handleSectionClick(`TestCase_${index}`)}
                        title={testCase.name}
                      >
                        <div className="flex-1 min-w-0">
                          <div className="line-clamp-2 break-words leading-tight">
                            {testCase.name}
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            </>
          )}

          {parsedSections.testCases.length === 0 && (
            <>
              <div className="border-t border-border mx-2"></div>
              <div className="flex-shrink-0 px-2 py-2">
                <div className="flex items-center justify-between px-3 py-1 bg-muted/50 rounded-md border">
                  <span className="text-xs font-bold text-foreground uppercase tracking-wide">
                    Test Cases (0)
                  </span>
                  <Button
                    onClick={() => setIsCreateModalOpen(true)}
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-primary/10 text-primary hover:text-primary"
                    title="Create Test Case"
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </>
          )}
        </div>
      </CardContent>

      <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create New Test Case</DialogTitle>
            <DialogDescription>
              Enter a name and documentation for your new test case.
              Documentation is optional.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-1 items-center gap-4">
              <Input
                placeholder="Enter test case name"
                value={newTestCaseName}
                onChange={(e) => setNewTestCaseName(e.target.value)}
                onKeyPress={handleKeyPress}
                disabled={isCreating}
                autoFocus
              />
              <Textarea
                placeholder="Enter documentation (optional)"
                value={newTestCaseDocumentation}
                onChange={(e) => setNewTestCaseDocumentation(e.target.value)}
                disabled={isCreating}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={handleModalClose}
              disabled={isCreating}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateTestCase}
              disabled={isCreating || !newTestCaseName.trim()}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isCreating && (
                <LoaderCircle className="animate-spin mr-2 h-4 w-4" />
              )}
              Create
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
