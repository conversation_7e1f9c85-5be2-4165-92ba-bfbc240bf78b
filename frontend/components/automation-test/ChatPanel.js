import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Send, LoaderCircle, Bot, User } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { getToken } from "@/lib/keycloak";
import { toast } from "sonner";
import { useSearchParams } from "next/navigation";
import ReactMarkdown from "react-markdown";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

const SPECIAL_BUTTON_MARKER = "+++button-add-test-case";

export default function ChatPanel({ onApplyTestCase }) {
  const searchParams = useSearchParams();
  const testSuiteId = searchParams.get("testSuiteId");

  const getChatKey = () => `chat_messages_${testSuiteId || "new"}`;

  const [messages, setMessages] = useState(() => {
    if (typeof window !== "undefined") {
      const savedMessages = sessionStorage.getItem(getChatKey());
      return savedMessages ? JSON.parse(savedMessages) : [];
    }
    return [];
  });

  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showProcessingMessage, setShowProcessingMessage] = useState(false);
  const [processingMessage, setProcessingMessage] = useState("");
  const messagesEndRef = useRef(null);

  const processingMessages = [
    "Analyzing your request...",
    "Gathering information...",
    "Processing data...",
    "Preparing response...",
    "Checking information...",
    "Fetching data...",
    "UrTest is thinking...",
    "Reviewing context...",
    "Analyzing question...",
    "Finding the best answer...",
  ];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, processingMessage]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      sessionStorage.setItem(getChatKey(), JSON.stringify(messages));
    }
  }, [messages, testSuiteId]);

  useEffect(() => {
    let interval;
    let messageIndex = 0;

    if (showProcessingMessage) {
      setProcessingMessage(processingMessages[messageIndex]);
      messageIndex = 1;

      interval = setInterval(() => {
        setProcessingMessage(
          processingMessages[messageIndex % processingMessages.length]
        );
        messageIndex++;
      }, 1500);
    } else {
      setProcessingMessage("");
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [showProcessingMessage]);

  const formatRobotFrameworkCode = (text) => {
    const lines = text.split(/\n|\\n/);
    let formattedLines = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      if (line.trim() && !line.startsWith("    ") && !line.startsWith("\t")) {
        if (i > 0) formattedLines.push("");
        formattedLines.push(line.trim());
      } else if (line.trim()) {
        const content = line.replace(/^\s+/, "").trim();
        if (content.startsWith("[Documentation]")) {
          formattedLines.push(
            `    [Documentation]    ${content
              .replace("[Documentation]", "")
              .trim()}`
          );
        } else {
          formattedLines.push(`    ${content}`);
        }
      } else {
        formattedLines.push("");
      }
    }

    return formattedLines.join("\n").trim();
  };

  const detectAndFormatRobotCode = (content) => {
    const robotKeywords = [
      "Log",
      "No Operation",
      "[Documentation]",
      "***",
      "Should Be Equal",
      "Should Contain",
      "Click Element",
      "Input Text",
    ];
    const hasRobotKeywords = robotKeywords.some((keyword) =>
      content.includes(keyword)
    );

    if (hasRobotKeywords && !content.includes("```")) {
      const formatted = `\`\`\`robotframework\n${formatRobotFrameworkCode(
        content
      )}\n\`\`\``;
      return formatted;
    }

    return content;
  };

  const sendMessage = async () => {
    if (!input.trim() || isLoading) return;

    const userMessage = { role: "user", content: input };
    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsLoading(true);
    setShowProcessingMessage(true);

    try {
      const token = getToken();
      if (!token) {
        throw new Error("No authentication token found");
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/ai/chat`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            messages: [userMessage],
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response
          .json()
          .catch(() => ({ error: "Failed to parse error response" }));
        throw new Error(errorData.error || "Failed to get AI response");
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let aiMessage = { role: "assistant", content: "" };
      setMessages((prev) => [...prev, aiMessage]);
      let firstContentReceived = false;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split("\n\n");

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            const dataString = line.substring(6).trim();
            if (dataString === "[DONE]") {
              break;
            }
            if (dataString) {
              try {
                const parsed = JSON.parse(dataString);
                if (parsed.content) {
                  if (!firstContentReceived) {
                    firstContentReceived = true;
                    setShowProcessingMessage(false);
                  }

                  aiMessage.content += parsed.content;
                  setMessages((prev) => {
                    const newMessages = [...prev];
                    if (
                      newMessages.length > 0 &&
                      newMessages[newMessages.length - 1].role === "assistant"
                    ) {
                      newMessages[newMessages.length - 1] = { ...aiMessage };
                    }
                    return newMessages;
                  });
                } else if (parsed.error) {
                  console.error("AI Error Chunk:", parsed.error);
                  aiMessage.content += `\n\n[AI Error: ${parsed.error}]`;
                  setMessages((prev) => {
                    const newMessages = [...prev];
                    if (
                      newMessages.length > 0 &&
                      newMessages[newMessages.length - 1].role === "assistant"
                    ) {
                      newMessages[newMessages.length - 1] = { ...aiMessage };
                    }
                    return newMessages;
                  });
                }
              } catch (e) {
                console.error("Failed to parse SSE data chunk:", dataString, e);
              }
            }
          }
        }
        if (chunk.includes("[DONE]")) break;
      }
    } catch (error) {
      console.error("Error sending message:", error);
      toast.error(error.message || "Failed to send message to AI");
      setMessages((prev) => {
        const newMessages = [...prev];
        if (
          newMessages.length > 0 &&
          newMessages[newMessages.length - 1].role === "assistant"
        ) {
          newMessages[newMessages.length - 1].content += `\n\n[Error: ${error.message || "Could not connect to AI"
            }]`;
        }
        return newMessages;
      });
    } finally {
      setIsLoading(false);
      setShowProcessingMessage(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const MarkdownComponents = {
    pre: ({ node, ...props }) => (
      <div className="relative my-3">
        <pre
          className="bg-slate-900 dark:bg-slate-800 text-slate-100 p-3 rounded-lg overflow-x-auto text-sm font-mono"
          style={{
            maxWidth: "100%",
            whiteSpace: "pre",
            overflowX: "auto",
            overflowY: "hidden",
            border: "none",
          }}
          {...props}
        />
      </div>
    ),
    code: ({ node, inline, className, children, ...props }) => {
      const match = /language-(\w+)/.exec(className || "");

      if (!inline && match) {
        return (
          <div className="relative my-3">
            <pre
              className="bg-slate-900 dark:bg-slate-800 text-slate-100 p-3 rounded-lg overflow-x-auto text-sm font-mono"
              style={{
                maxWidth: '100%',
                whiteSpace: 'pre',
                overflowX: 'auto',
                overflowY: 'hidden',
                border: 'none'
              }}
            >
              <code
                className={`${className || ''}`}
                style={{
                  whiteSpace: 'pre',
                  wordBreak: 'normal',
                  overflowWrap: 'normal'
                }}
                {...props}
              >
                {children}
              </code>
            </pre>
          </div>
        );
      }

      return (
        <code
          className="bg-slate-100 dark:bg-slate-700 px-1.5 py-0.5 rounded text-sm font-mono text-slate-800 dark:text-slate-200"
          {...props}
        >
          {children}
        </code>
      );
    },
    p: ({ node, ...props }) => (
      <p className="mb-2 leading-relaxed" {...props} />
    ),
  };

  return (
    <Card className="h-full flex flex-col bg-card border-border">
      <CardContent
        className="flex-1 flex flex-col p-4 overflow-hidden"
        style={{ width: "100%" }}
      >
        <div
          className="flex-1 overflow-y-auto mb-4"
          style={{
            maxHeight: "calc(100vh - 280px)",
            width: "100%",
            overflowX: "hidden",
          }}
        >
          <div className="space-y-4" style={{ width: "100%" }}>
            {messages.map((message, index) => {
              let displayContent = message.content;
              let codeToApply = null;
              let showApplyButton = false;

              if (message.role === "assistant" && message.content) {
                const trimmedContent = message.content.trim();

                if (trimmedContent.endsWith(SPECIAL_BUTTON_MARKER)) {
                  displayContent = trimmedContent
                    .substring(
                      0,
                      trimmedContent.length - SPECIAL_BUTTON_MARKER.length
                    )
                    .trim();

                  if (displayContent.includes("```robotframework")) {
                    codeToApply = displayContent
                      .replace(/^\s*```robotframework\s*\n?/, "")
                      .replace(/\s*```\s*$/, "")
                      .trim();
                    showApplyButton = true;
                  }
                } else {
                  displayContent = detectAndFormatRobotCode(displayContent);

                  const robotKeywords = [
                    "Log",
                    "No Operation",
                    "[Documentation]",
                    "Should Be Equal",
                    "Should Contain",
                  ];
                  const hasRobotKeywords = robotKeywords.some((keyword) =>
                    displayContent.includes(keyword)
                  );

                  if (
                    hasRobotKeywords &&
                    displayContent.includes("```robotframework")
                  ) {
                    showApplyButton = true;
                    codeToApply = displayContent
                      .replace(/^\s*```robotframework\s*\n?/, "")
                      .replace(/\s*```\s*$/, "")
                      .trim();
                  }
                }
              }

              return (
                <div
                  key={index}
                  className="my-2"
                >
                  {message.role === "assistant" ? (
                    <div
                      className="w-full bg-muted text-foreground rounded-lg p-3 break-words"
                      style={{ overflowX: "hidden" }}
                    >
                      <div className="flex gap-3 mb-2">
                        <Avatar className="flex-shrink-0 h-6 w-6">
                          <AvatarFallback className="bg-primary text-primary-foreground">
                            <Bot size={16} />
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm font-medium text-foreground">
                          AI Assistant
                        </span>
                      </div>
                      <div className="text-sm">
                        <ReactMarkdown components={MarkdownComponents}>
                          {displayContent || ""}
                        </ReactMarkdown>
                      </div>
                      {showApplyButton && codeToApply && onApplyTestCase && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="mt-3 bg-primary-foreground hover:bg-primary-foreground/90 text-primary border-primary/20"
                          onClick={() => onApplyTestCase(codeToApply)}
                        >
                          Apply Test Case
                        </Button>
                      )}
                    </div>
                  ) : (
                    <div
                      className="w-full bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800 text-foreground rounded-lg p-3 break-words"
                      style={{ overflowX: "hidden" }}
                    >
                      <div className="flex gap-3 mb-2">
                        <Avatar className="flex-shrink-0 h-6 w-6">
                          <AvatarFallback className="bg-blue-500 text-white">
                            <User size={16} />
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                          You
                        </span>
                      </div>
                      <div className="text-sm">
                        <ReactMarkdown components={MarkdownComponents}>
                          {displayContent || ""}
                        </ReactMarkdown>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}

            {showProcessingMessage && processingMessage && (
              <div className="my-2">
                <div className="w-full bg-muted text-foreground rounded-lg p-3">
                  <div className="flex gap-3 mb-2">
                    <Avatar className="flex-shrink-0 h-6 w-6">
                      <AvatarFallback className="bg-primary text-primary-foreground">
                        <Bot size={16} />
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-sm font-medium text-foreground">
                      AI Assistant
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <LoaderCircle className="h-4 w-4 animate-spin" />
                    <span>{processingMessage}</span>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </div>
        <div className="flex gap-2 border-t border-border pt-4">
          <Input
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask about your test case..."
            disabled={isLoading}
            className="flex-1 bg-background border-input text-foreground placeholder:text-muted-foreground"
          />
          <Button
            onClick={sendMessage}
            disabled={isLoading || !input.trim()}
            size="sm"
            className="bg-primary text-primary-foreground hover:bg-primary/90"
          >
            {isLoading ? (
              <LoaderCircle className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
