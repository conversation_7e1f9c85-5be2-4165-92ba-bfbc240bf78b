"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import {
  Cloud,
  Play,
  CheckCircle,
  Loader2,
  Eye,
  FileText
} from "lucide-react";
import { toast } from "sonner";
import AppFileSelector from "@/components/AppFileSelector";
import { getToken } from "@/lib/keycloak";

export default function DeviceFarm() {
  const [devicePools, setDevicePools] = useState([]);
  const [selectedDevicePool, setSelectedDevicePool] = useState('');
  const [testFiles, setTestFiles] = useState([{ name: 'test.robot', content: '' }]);
  const [appFile, setAppFile] = useState({ name: '', path: '', platform: 'android' });
  const [runName, setRunName] = useState('');
  const [appPackage, setAppPackage] = useState('');
  const [appActivity, setAppActivity] = useState('');
  const [loading, setLoading] = useState(false);
  const [runStatus, setRunStatus] = useState(null);
  const [testResults, setTestResults] = useState(null);
  const [template, setTemplate] = useState('');

  useEffect(() => {
    loadDevicePools();
    loadTestTemplate();
  }, []);

  const makeRequest = async (endpoint, options = {}) => {
    try {
      const token = getToken();

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        ...options
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || `HTTP ${response.status}`);
      }
      
      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  };

  const loadDevicePools = async () => {
    try {
      const data = await makeRequest('/api/device-farm/device-pools');
      setDevicePools(data.devicePools || []);
    } catch (error) {
      console.error('Failed to load device pools:', error);
      toast.error('Failed to load device pools');
    }
  };

  const loadTestTemplate = async () => {
    try {
      const data = await makeRequest('/api/device-farm/test-template?platform=android');
      setTemplate(data.template || '');
      
      // Set first test file with template
      if (testFiles.length > 0 && !testFiles[0].content) {
        setTestFiles([{ ...testFiles[0], content: data.template }]);
      }
    } catch (error) {
      console.error('Failed to load test template:', error);
    }
  };

  const addTestFile = () => {
    setTestFiles([...testFiles, { name: `test${testFiles.length + 1}.robot`, content: '' }]);
  };

  const updateTestFile = (index, field, value) => {
    const updated = [...testFiles];
    updated[index][field] = value;
    setTestFiles(updated);
  };

  const removeTestFile = (index) => {
    if (testFiles.length > 1) {
      const updated = testFiles.filter((_, i) => i !== index);
      setTestFiles(updated);
    }
  };

  const runTests = async () => {
    try {
      if (!selectedDevicePool) {
        toast.error('Please select a device pool');
        return;
      }

      if (!runName) {
        toast.error('Please enter a run name');
        return;
      }

      if (!appFile.name) {
        toast.error('Please specify app file information');
        return;
      }

      if (testFiles.some(f => !f.content.trim())) {
        toast.error('Please fill in all test files');
        return;
      }

      setLoading(true);
      setRunStatus(null);
      setTestResults(null);

      const runData = {
        testFiles: testFiles.map(f => ({ name: f.name, content: f.content })),
        appFile: {
          name: appFile.name,
          path: appFile.path,
          platform: appFile.platform,
          arn: appFile.arn || ''
        },
        runName: runName,
        devicePoolArn: selectedDevicePool,
        appPackage: appPackage,
        appActivity: appActivity
      };

      const result = await makeRequest('/api/device-farm/run-robot-tests', {
        method: 'POST',
        body: JSON.stringify(runData)
      });
      
      // Start monitoring the run
      monitorRun(result.runArn);

    } catch (error) {
      console.error('Failed to run tests:', error);
      toast.error(`Failed to run tests: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const monitorRun = async (runArn) => {
    try {
      const 

      // Use polling instead of EventSource since EventSource doesn't support custom headers
      const pollInterval = setInterval(async () => {
        try {
          const data = await makeRequest(`/api/device-farm/runs/${runArn}/status`);

          setRunStatus(data);

          // Check if run is completed
          if (data.status === 'COMPLETED' || data.status === 'FAILED' || data.status === 'STOPPED') {
            clearInterval(pollInterval);

            // Load test results
            loadTestResults(runArn);

            if (data.status === 'COMPLETED') {
              if (data.result === 'PASSED') {
                toast.success('Test run completed successfully!');
              } else {
                toast.error(`Test run completed with result: ${data.result}`);
              }
            } else if (data.status === 'FAILED') {
              toast.error('Test run failed');
            } else if (data.status === 'STOPPED') {
              toast.warning('Test run was stopped');
            }
          }
        } catch (error) {
          console.error('❌ Polling error:', error);
          clearInterval(pollInterval);

          if (error.message.includes('401') || error.message.includes('Authentication')) {
            toast.error('Authentication failed. Please login again.');
          } else {
            toast.error(`Monitoring failed: ${error.message}`);
          }
        }
      }, 5000); // Poll every 5 seconds

      // Store interval ID for cleanup
      window.currentPollInterval = pollInterval;

    } catch (error) {
      console.error('❌ Failed to start monitoring:', error);
      toast.error('Failed to start monitoring');
    }
  };

  const loadTestResults = async (runArn) => {
    try {
      const data = await makeRequest(`/api/device-farm/runs/${runArn}/results`);
      setTestResults(data.results);
    } catch (error) {
      console.error('Failed to load test results:', error);
      toast.error('Failed to load test results');
    }
  };

  const getStatusBadge = (status, result) => {
    if (status === 'COMPLETED') {
      return result === 'PASSED' ? 
        <Badge className="bg-green-100 text-green-800 border-green-200">Passed</Badge> :
        <Badge variant="destructive">Failed</Badge>;
    } else if (status === 'RUNNING') {
      return <Badge variant="outline">Running</Badge>;
    } else if (status === 'PENDING') {
      return <Badge variant="secondary">Pending</Badge>;
    } else {
      return <Badge variant="destructive">Error</Badge>;
    }
  };

  return (
    <div className="space-y-6 p-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold flex items-center justify-center gap-2">
          <Cloud className="h-6 w-6" />
          AWS Device Farm
        </h1>
        <p className="text-muted-foreground mt-2">
          Run Robot Framework tests on real AWS devices
        </p>
      </div>

      {/* Test Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Test Configuration</CardTitle>
          <CardDescription>
            Configure your Robot Framework tests for AWS Device Farm
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Run Name */}
          <div>
            <label className="text-sm font-medium">Run Name</label>
            <Input
              placeholder="Enter test run name"
              value={runName}
              onChange={(e) => setRunName(e.target.value)}
            />
          </div>

          {/* Device Pool */}
          <div>
            <label className="text-sm font-medium">Device Pool</label>
            <Select value={selectedDevicePool} onValueChange={setSelectedDevicePool}>
              <SelectTrigger>
                <SelectValue placeholder="Select device pool" />
              </SelectTrigger>
              <SelectContent>
                {devicePools.map((pool) => (
                  <SelectItem key={pool.arn} value={pool.arn}>
                    {pool.name} ({pool.description})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* App Configuration */}
          <div>
            <label className="text-sm font-medium mb-2 block">App Configuration</label>
            <AppFileSelector
              onAppSelected={(app) => setAppFile(app)}
              selectedApp={appFile}
            />
          </div>

          {/* App Package & Activity (for Android) */}
          {appFile.platform === 'android' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">App Package</label>
                <Input
                  placeholder="com.example.app"
                  value={appPackage}
                  onChange={(e) => setAppPackage(e.target.value)}
                />
              </div>
              <div>
                <label className="text-sm font-medium">App Activity</label>
                <Input
                  placeholder=".MainActivity"
                  value={appActivity}
                  onChange={(e) => setAppActivity(e.target.value)}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test Files */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Robot Framework Test Files
            <Button variant="outline" size="sm" onClick={addTestFile}>
              <FileText className="h-4 w-4 mr-2" />
              Add File
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {testFiles.map((file, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center gap-2">
                <Input
                  placeholder="test.robot"
                  value={file.name}
                  onChange={(e) => updateTestFile(index, 'name', e.target.value)}
                  className="flex-1"
                />
                {testFiles.length > 1 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeTestFile(index)}
                  >
                    Remove
                  </Button>
                )}
              </div>
              <Textarea
                placeholder="Enter Robot Framework test content..."
                value={file.content}
                onChange={(e) => updateTestFile(index, 'content', e.target.value)}
                rows={10}
                className="font-mono text-sm"
              />
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Run Tests */}
      <Card>
        <CardContent className="pt-6">
          <Button 
            onClick={runTests} 
            disabled={loading}
            className="w-full"
            size="lg"
          >
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Play className="h-4 w-4 mr-2" />
            )}
            Run Tests on AWS Device Farm
          </Button>
        </CardContent>
      </Card>

      {/* Run Status */}
      {runStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Test Run Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>Status:</span>
                {getStatusBadge(runStatus.status, runStatus.result)}
              </div>
              
              {runStatus.totalJobs && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span>Progress:</span>
                    <span>{runStatus.completedJobs || 0}/{runStatus.totalJobs}</span>
                  </div>
                  <Progress 
                    value={((runStatus.completedJobs || 0) / runStatus.totalJobs) * 100} 
                    className="w-full"
                  />
                </div>
              )}
              
              {runStatus.message && (
                <div>
                  <span className="font-medium">Message:</span>
                  <p className="text-sm text-muted-foreground mt-1">{runStatus.message}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Test Results */}
      {testResults && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Test Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {testResults.map((job, jobIndex) => (
                <div key={jobIndex} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">
                      {job.device.name} - {job.device.platform} {job.device.os}
                    </h4>
                    {getStatusBadge(job.status, job.result)}
                  </div>
                  
                  {job.suites.map((suite, suiteIndex) => (
                    <div key={suiteIndex} className="ml-4 mt-2">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-sm">{suite.name}</span>
                        {getStatusBadge(suite.status, suite.result)}
                      </div>
                      
                      {suite.tests.map((test, testIndex) => (
                        <div key={testIndex} className="ml-4 mt-1 text-sm">
                          <div className="flex items-center gap-2">
                            <span>{test.name}</span>
                            {getStatusBadge(test.status, test.result)}
                          </div>
                          {test.message && (
                            <p className="text-xs text-muted-foreground mt-1">
                              {test.message}
                            </p>
                          )}
                        </div>
                      ))}
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
