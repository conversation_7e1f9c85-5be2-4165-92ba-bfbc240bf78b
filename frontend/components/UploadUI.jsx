import { CloudUpload, Trash2 } from "lucide-react";
import { Fragment, useRef, useState } from "react";

const UploadUI = ({
  mime = ".xls,.xlsx,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  file,
  setFile,
}) => {
  const inputRef = useRef(null);

  const handleDivClick = () => {
    inputRef.current.click();
  };

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile && setFile) {
      setFile(selectedFile);
    }
  };

  return (
    <Fragment>
      <div
        onClick={handleDivClick}
        className="border-dashed border cursor-pointer hover:bg-muted flex flex-col items-center justify-center rounded-sm w-full h-52"
      >
        <CloudUpload className="h-7 w-7" />
        {file ? (
          <div className="flex gap-3 items-center mt-2 text-sm text-green-600">
            <span>
              {file.name} ({(file.size / 1024).toFixed(2)} KB)
            </span>
            <div
              className="bg-muted-foreground/20 p-2 flex items-center justify-center rounded-sm"
              onClick={(e) => {
                e.stopPropagation();
                setFile(null);
              }}
            >
              <Trash2 className="text-red-500 h-4 w-4" />
            </div>
          </div>
        ) : (
          <div className="text-sm text-muted-foreground mt-2">Chọn file mà bạn muốn tải lên</div>
        )}
      </div>
      <input type="file" ref={inputRef} className="hidden" accept={mime} onChange={handleFileChange} />
    </Fragment>
  );
};
export default UploadUI;
