import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Search,
  ChevronLeft,
  ChevronRight,
  LoaderCircle,
  Clock,
  User,
  FileText,
  Bug,
  Trash2,
  Edit,
  Plus,
  Upload,
  CheckCircle,
} from "lucide-react";
import { Fragment, useMemo, useState } from "react";
import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { useQuery } from "@tanstack/react-query";
import { ManualTestApi } from "@/lib/api";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";

dayjs.extend(relativeTime);

const getActionIcon = (actionType) => {
  const iconMap = {
    CREATE_TEST_CASE: <Plus className="h-4 w-4 text-green-600" />,
    UPDATE_TEST_CASE: <Edit className="h-4 w-4 text-blue-600" />,
    DELETE_TEST_CASE: <Trash2 className="h-4 w-4 text-red-600" />,
    EXECUTE_TEST_CASE: <CheckCircle className="h-4 w-4 text-purple-600" />,
    UPDATE_TEST_CASE_STATUS: <Clock className="h-4 w-4 text-orange-600" />,
    CREATE_BUG: <Bug className="h-4 w-4 text-red-600" />,
    UPDATE_BUG: <Bug className="h-4 w-4 text-orange-600" />,
    UPDATE_BUG_STATUS: <Bug className="h-4 w-4 text-blue-600" />,
    DELETE_BUG: <Trash2 className="h-4 w-4 text-red-600" />,
    UPLOAD_FILE: <Upload className="h-4 w-4 text-green-600" />,
    DELETE_FILE: <Trash2 className="h-4 w-4 text-red-600" />,
    BULK_CREATE_TEST_CASES: <Plus className="h-4 w-4 text-green-600" />,
  };
  return iconMap[actionType] || <FileText className="h-4 w-4 text-gray-600" />;
};

const getActionTypeLabel = (actionType) => {
  const labelMap = {
    CREATE_TEST_CASE: "Create Test Case",
    UPDATE_TEST_CASE: "Update Test Case",
    DELETE_TEST_CASE: "Delete Test Case",
    EXECUTE_TEST_CASE: "Execute Test Case",
    UPDATE_TEST_CASE_STATUS: "Update Status",
    CREATE_BUG: "Create Bug",
    UPDATE_BUG: "Update Bug",
    UPDATE_BUG_STATUS: "Update Bug Status",
    DELETE_BUG: "Delete Bug",
    UPLOAD_FILE: "Upload File",
    DELETE_FILE: "Delete File",
    BULK_CREATE_TEST_CASES: "Bulk Create",
  };
  return labelMap[actionType] || actionType;
};

const getTargetTypeColor = (targetType) => {
  const colorMap = {
    TEST_CASE: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
    BUG: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
    FILE: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
    PROJECT:
      "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
  };
  return colorMap[targetType] || "bg-gray-100 text-gray-800";
};

export default function ManualTestHistoryList({ project }) {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(0);
  const pageSize = 10;

  const { data: historyData, isLoading: isLoadingHistory } = useQuery({
    queryKey: ["manual-test-history", project.id, currentPage, searchQuery],
    queryFn: () =>
      ManualTestApi().getHistory({
        projectId: project.id,
        limit: pageSize.toString(),
        offset: (currentPage * pageSize).toString(),
        userEmail: searchQuery.includes("@") ? searchQuery : undefined,
      }),
    enabled: !!project.id,
  });

  const columns = useMemo(() => {
    return [
      {
        accessorKey: "actionType",
        header: "Action",
        cell: ({ row }) => {
          const actionType = row.getValue("actionType");
          return (
            <div className="flex items-center gap-2">
              {getActionIcon(actionType)}
              <span className="text-sm font-medium">
                {getActionTypeLabel(actionType)}
              </span>
            </div>
          );
        },
      },
      {
        accessorKey: "targetType",
        header: "Target",
        cell: ({ row }) => {
          const targetType = row.getValue("targetType");
          return (
            <Badge variant="outline" className={getTargetTypeColor(targetType)}>
              {targetType?.replace("_", " ")}
            </Badge>
          );
        },
      },
      {
        accessorKey: "description",
        header: "Description",
        cell: ({ row }) => {
          const description = row.getValue("description");
          const testCase = row.original.testCase;
          return (
            <div className="max-w-md">
              <p className="text-sm font-medium line-clamp-2">{description}</p>
              {testCase && (
                <p className="text-xs text-muted-foreground mt-1">
                  Test Case: {testCase.name}
                </p>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: "userEmail",
        header: "User",
        cell: ({ row }) => {
          const userEmail = row.getValue("userEmail");
          const initials = userEmail
            .split("@")[0]
            .substring(0, 2)
            .toUpperCase();
          return (
            <div className="flex items-center gap-2">
              <Avatar className="h-6 w-6">
                <AvatarFallback className="text-xs">{initials}</AvatarFallback>
              </Avatar>
              <span className="text-sm">{userEmail.split("@")[0]}</span>
            </div>
          );
        },
      },
      {
        accessorKey: "createdAt",
        header: "When",
        cell: ({ row }) => {
          const createdAt = row.getValue("createdAt");
          return (
            <div className="text-sm">
              <div>{dayjs(createdAt).fromNow()}</div>
              <div className="text-xs text-muted-foreground">
                {dayjs(createdAt).format("MMM DD, YYYY HH:mm")}
              </div>
            </div>
          );
        },
      },
    ];
  }, []);

  const historyRecords = historyData?.history || [];
  const pagination = historyData?.pagination || {};

  const filteredData = useMemo(() => {
    let filtered = historyRecords;

    if (searchQuery && !searchQuery.includes("@")) {
      filtered = filtered.filter(
        (record) =>
          record.description
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          record.actionType.toLowerCase().includes(searchQuery.toLowerCase()) ||
          record.targetType.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return filtered;
  }, [historyRecords, searchQuery]);

  const table = useReactTable({
    data: filteredData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    pageCount: Math.ceil((pagination.total || 0) / pageSize),
  });

  const handlePreviousPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (pagination.hasMore) {
      setCurrentPage(currentPage + 1);
    }
  };

  return (
    <div className="border rounded-lg bg-card overflow-hidden shadow-lg mx-2 mt-4">
      <div className="px-4 py-4 border-b bg-muted/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-6">
            <h2 className="text-lg font-semibold">History</h2>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative w-[280px]">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search history or user email..."
                className="pl-10 h-9 w-full text-sm"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </div>
      </div>

      <div className="p-3">
        <div className="w-full border rounded-md overflow-hidden bg-card">
          <Table className="w-full">
            <TableHeader className="bg-muted/50">
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead
                      key={header.id}
                      className="py-2 px-3 text-sm font-semibold text-foreground"
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isLoadingHistory ? (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-32 text-center"
                  >
                    <div className="flex items-center justify-center">
                      <LoaderCircle className="h-6 w-6 animate-spin text-primary" />
                      <span className="ml-2">Loading history...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    className="border-b hover:bg-muted/30 transition-colors"
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id} className="py-3 px-3">
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-32 text-center"
                  >
                    <div className="flex flex-col items-center justify-center text-muted-foreground">
                      <div className="text-lg mb-2">
                        {searchQuery ? "🔍" : "📝"}
                      </div>
                      <div className="text-sm">
                        {searchQuery
                          ? "No history found matching your search criteria."
                          : "No history records found for this project."}
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        <div className="flex items-center justify-between mt-6">
          <div className="text-sm text-muted-foreground">
            Showing {filteredData.length} of {pagination.total || 0} records
            {pagination.hasMore && " (more available)"}
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={handlePreviousPage}
              disabled={currentPage === 0}
              className="h-8 w-8 rounded-md p-0"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="default"
              size="sm"
              className="h-8 px-3 rounded-md bg-primary text-primary-foreground text-sm"
            >
              {currentPage + 1}
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={handleNextPage}
              disabled={!pagination.hasMore}
              className="h-8 w-8 rounded-md p-0"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
