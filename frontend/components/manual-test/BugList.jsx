import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { ManualTestApi } from "@/lib/api";
import {
  LoaderCircle,
  AlertTriangle,
  Inbox,
  Edit,
  Paperclip,
  Eye,
  User,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { useState } from "react";
import EditBugModal from "./EditBugModal";

dayjs.extend(relativeTime);

const BugItem = ({ bug, onUpdateStatus, onEdit }) => {
  const getSeverityBadgeClass = (severity) => {
    switch (severity) {
      case "Critical":
        return "bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300 border-red-200 dark:border-red-700";
      case "High":
        return "bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300 border-orange-200 dark:border-orange-700";
      case "Medium":
        return "bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300 border-yellow-200 dark:border-yellow-700";
      case "Low":
        return "bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 border-green-200 dark:border-green-700";
      default:
        return "bg-gray-100 text-gray-700 dark:bg-gray-900 dark:text-gray-300 border-gray-200 dark:border-gray-700";
    }
  };

  const getStatusStyles = (status) => {
    switch (status) {
      case "Open":
        return {
          trigger:
            "bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 dark:bg-blue-900/20 dark:border-blue-700 dark:text-blue-300",
          dot: "bg-blue-500",
        };
      case "In Progress":
        return {
          trigger:
            "bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100 dark:bg-orange-900/20 dark:border-orange-700 dark:text-orange-300",
          dot: "bg-orange-500",
        };
      case "Resolved":
        return {
          trigger:
            "bg-green-50 border-green-200 text-green-700 hover:bg-green-100 dark:bg-green-900/20 dark:border-green-700 dark:text-green-300",
          dot: "bg-green-500",
        };
      case "Reopened":
        return {
          trigger:
            "bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100 dark:bg-purple-900/20 dark:border-purple-700 dark:text-purple-300",
          dot: "bg-purple-500",
        };
      case "Closed":
        return {
          trigger:
            "bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100 dark:bg-gray-900/20 dark:border-gray-700 dark:text-gray-300",
          dot: "bg-gray-500",
        };
      default:
        return {
          trigger: "bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100",
          dot: "bg-gray-500",
        };
    }
  };

  const statusOptions = [
    {
      value: "Open",
      label: "Open",
      color: "text-blue-600 dark:text-blue-400",
      dot: "bg-blue-500",
    },
    {
      value: "In Progress",
      label: "In Progress",
      color: "text-orange-600 dark:text-orange-400",
      dot: "bg-orange-500",
    },
    {
      value: "Resolved",
      label: "Resolved",
      color: "text-green-600 dark:text-green-400",
      dot: "bg-green-500",
    },
    {
      value: "Reopened",
      label: "Reopened",
      color: "text-purple-600 dark:text-purple-400",
      dot: "bg-purple-500",
    },
    {
      value: "Closed",
      label: "Closed",
      color: "text-gray-600 dark:text-gray-400",
      dot: "bg-gray-500",
    },
  ];

  const handleStatusChange = (newStatus) => {
    if (newStatus !== bug.status) {
      onUpdateStatus(bug.id, newStatus);
    }
  };

  const handleViewFile = (e, fileUrl) => {
    e.preventDefault();
    e.stopPropagation();
    window.open(fileUrl, "_blank");
  };

  const handleEditClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    onEdit(bug);
  };

  const attachments = bug.attachments || [];
  const currentStatusStyles = getStatusStyles(bug.status);

  return (
    <div
      key={bug.id}
      className="p-4 border rounded-lg bg-card hover:shadow-md transition-shadow"
    >
      <div className="flex justify-between items-start mb-3">
        <h4 className="font-semibold text-md text-foreground flex-1 pr-4">
          {bug.title}
        </h4>
        <div className="flex flex-col items-end flex-shrink-0">
          <div className="flex items-center gap-3">
            <Badge
              variant="outline"
              className={`text-xs ${getSeverityBadgeClass(bug.severity)}`}
            >
              {bug.severity}
            </Badge>
            <Select value={bug.status} onValueChange={handleStatusChange}>
              <SelectTrigger
                className={`w-[120px] h-8 text-xs ${currentStatusStyles.trigger}`}
              >
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      <div
                        className={`w-2 h-2 rounded-full ${option.dot}`}
                      ></div>
                      <span className={option.color}>{option.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4 text-xs text-muted-foreground">
          {bug.assignedToEmail ? (
            <div className="flex items-center gap-1">
              <User className="h-3 w-3" />
              <span>{bug.assignedToEmail.split("@")[0]}</span>
            </div>
          ) : (
            <div className="flex items-center gap-1 text-gray-400">
              <User className="h-3 w-3" />
              <span>Unassigned</span>
            </div>
          )}

          <span>{dayjs(bug.createdAt).fromNow()}</span>

          {attachments.length > 0 && (
            <div className="flex items-center gap-1 text-blue-600 dark:text-blue-400">
              <Paperclip className="h-3 w-3" />
              <span className="font-medium">
                {attachments.length} file{attachments.length > 1 ? "s" : ""}
              </span>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="text-xs h-7 px-3"
            onClick={handleEditClick}
          >
            <Edit className="h-3 w-3 mr-1" />
            Edit
          </Button>
        </div>
      </div>

      {bug.description && (
        <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-800">
          <p className="text-sm text-muted-foreground line-clamp-2">
            {bug.description}
          </p>
        </div>
      )}

      {attachments.length > 0 && (
        <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-800">
          <div className="flex flex-wrap gap-2">
            {attachments.map((attachment) => (
              <div
                key={attachment.id}
                className="flex items-center gap-1 px-3 py-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-md text-xs hover:bg-blue-100 dark:hover:bg-blue-900/30 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 cursor-pointer"
              >
                <span
                  className="truncate max-w-[120px] text-blue-700 dark:text-blue-300 font-medium"
                  title={attachment.originalName}
                >
                  {attachment.originalName}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 ml-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
                  onClick={(e) => handleViewFile(e, attachment.url)}
                  title={`View ${attachment.originalName}`}
                >
                  <Eye className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

const BugList = ({ testCaseId }) => {
  const queryClient = useQueryClient();
  const [isEditBugModalOpen, setIsEditBugModalOpen] = useState(false);
  const [selectedBug, setSelectedBug] = useState(null);

  const {
    data: bugs,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ["bugs-for-test-case", testCaseId],
    queryFn: () => ManualTestApi().getBugsForTestCase(testCaseId),
    enabled: !!testCaseId,
  });

  const updateBugStatusMutation = useMutation({
    mutationFn: ({ bugId, status }) =>
      ManualTestApi().updateBugStatus(bugId, status),
    onMutate: async ({ bugId, status }) => {
      await queryClient.cancelQueries(["bugs-for-test-case", testCaseId]);

      const previousBugs = queryClient.getQueryData([
        "bugs-for-test-case",
        testCaseId,
      ]);

      queryClient.setQueryData(["bugs-for-test-case", testCaseId], (old) => {
        if (!old) return old;
        return old.map((bug) => (bug.id === bugId ? { ...bug, status } : bug));
      });

      return { previousBugs };
    },
    onError: (error, variables, context) => {
      if (context?.previousBugs) {
        queryClient.setQueryData(
          ["bugs-for-test-case", testCaseId],
          context.previousBugs
        );
      }
      toast.error(
        error.response?.data?.message || "Failed to update bug status"
      );
    },
    onSettled: () => {
      queryClient.invalidateQueries(["bugs-for-test-case", testCaseId]);
    },
  });

  const handleUpdateStatus = (bugId, status) => {
    updateBugStatusMutation.mutate({ bugId, status });
  };

  const handleEditBug = (bug) => {
    setSelectedBug(bug);
    setIsEditBugModalOpen(true);
  };

  const handleBugUpdated = () => {
    queryClient.invalidateQueries(["bugs-for-test-case", testCaseId]);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-4">
        <LoaderCircle className="h-6 w-6 animate-spin text-primary" />
        <span className="ml-2 text-muted-foreground">Loading bugs...</span>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center py-4 text-red-500">
        <AlertTriangle className="h-8 w-8 mb-2" />
        <p>Error loading bugs: {error.message}</p>
      </div>
    );
  }

  if (!bugs || bugs.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-6 text-muted-foreground">
        <Inbox className="h-10 w-10 mb-2 text-gray-400 dark:text-gray-500" />
        <p>No bugs reported for this test case yet.</p>
        <Button onClick={() => refetch()} variant="link" className="mt-2">
          Refresh
        </Button>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-3 mt-4 max-h-[calc(100vh-380px)] overflow-y-auto pr-2 pb-4">
        {bugs.map((bug) => (
          <BugItem
            key={bug.id}
            bug={bug}
            onUpdateStatus={handleUpdateStatus}
            onEdit={handleEditBug}
          />
        ))}
      </div>

      <EditBugModal
        open={isEditBugModalOpen}
        setOpen={setIsEditBugModalOpen}
        bug={selectedBug}
        projectId={selectedBug?.projectId}
        onBugUpdated={handleBugUpdated}
      />
    </>
  );
};

export default BugList;
