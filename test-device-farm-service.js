#!/usr/bin/env node

console.log('🧪 Testing AWS Device Farm Service Import');
console.log('========================================');

async function testServiceImport() {
  try {
    // Test CommonJS require
    console.log('1. Testing CommonJS require...');
    const serviceCommonJS = require('./backend/src/services/awsDeviceFarmService');
    console.log('✅ CommonJS import successful');
    console.log('Service methods:', Object.getOwnPropertyNames(serviceCommonJS));
    console.log('createRobotTestPackage method:', typeof serviceCommonJS.createRobotTestPackage);
    
    // Test if methods exist
    const expectedMethods = [
      'createRobotTestPackage',
      'generateRobotTestSpec',
      'uploadFile',
      'scheduleRun',
      'getDevicePools',
      'getDevicesInPool',
      'monitorRun',
      'getTestResults'
    ];
    
    console.log('\\n2. Checking required methods...');
    expectedMethods.forEach(method => {
      const exists = typeof serviceCommonJS[method] === 'function';
      console.log(`${exists ? '✅' : '❌'} ${method}: ${exists ? 'exists' : 'missing'}`);
    });
    
    // Test createRobotTestPackage method
    console.log('\\n3. Testing createRobotTestPackage method...');
    const testFiles = [
      {
        name: 'test.robot',
        content: `*** Test Cases ***
Sample Test
    Log    Hello World`
      }
    ];
    
    const tempDir = './temp';
    const fs = require('fs');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    try {
      const packagePath = await serviceCommonJS.createRobotTestPackage(testFiles, tempDir);
      console.log('✅ createRobotTestPackage method works!');
      console.log('Package created at:', packagePath);
      
      // Check if file exists
      if (fs.existsSync(packagePath)) {
        const stats = fs.statSync(packagePath);
        console.log('✅ Package file exists, size:', stats.size, 'bytes');
      } else {
        console.log('❌ Package file does not exist');
      }
    } catch (error) {
      console.log('❌ createRobotTestPackage failed:', error.message);
    }
    
    console.log('\\n✅ Service import test completed successfully!');
    
  } catch (error) {
    console.error('❌ Service import test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

testServiceImport();
