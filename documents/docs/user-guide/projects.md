---
sidebar_position: 2
---

# Project Management

Projects are the top-level organizational units in UrTest, containing test suites and resources.

## Creating a Project

To create a new project:

1. Click on "Dashboard" in the sidebar
2. Click the "Create Project" button
3. Enter a name and description for your project
4. Click "Create" to save your new project

## Project Dashboard

The project dashboard shows you:

- Recent test runs within the project
- Test suites in the project
- Test resources in the project
- Project statistics and metrics

## Managing Project Settings

To modify project settings:

1. Navigate to your project
2. Click the "Edit Project" button
3. Update the project name or description
4. Click "Save" to apply your changes

## Assigning Staff to Projects

To assign team members to a project:

1. Navigate to your project
2. Click the "Manage Staff" button
3. Select users from the available staff list
4. Click "Add" to assign them to the project

Only assigned staff members can access and modify the project's test suites and resources.

## Deleting a Project

To delete a project:

1. Navigate to your project
2. Click the "Edit Project" button
3. Click the "Delete Project" button
4. Confirm deletion in the dialog

:::warning
Deleting a project will remove all test suites and resources associated with it. This action cannot be undone.
:::
