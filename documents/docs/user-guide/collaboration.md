---
sidebar_position: 6
---

# Collaboration Features

UrTest provides several features to help teams collaborate effectively on test automation.

## Comments System

The comments system allows team members to discuss test cases and resources:

### Adding Comments

1. Navigate to a test suite
2. Click on the "Comments" tab
3. Type your comment in the input field
4. Click "Send" to post your comment

### Managing Comments

- Comments are displayed in chronological order
- You can edit or delete your own comments
- Comments are attached to the specific test suite or resource

## Staff Assignment

Assign team members to specific projects:

### Assigning Staff

1. Navigate to your project
2. Click the "Manage Staff" button
3. Select users from the available staff list
4. Click "Add" to assign them to the project

### Managing Access

- Only assigned staff can access and modify a project
- Managers and admins can assign or remove staff
- Users can be assigned to multiple projects

## AI Assistant

Get help from the integrated AI assistant:

### Using the AI Assistant

1. Navigate to a test suite
2. Click on the "Assistant" tab
3. Type your question or request
4. The AI will provide suggestions and help

The AI assistant can help with:

- Writing test cases
- Explaining Robot Framework concepts
- Providing best practices
- Troubleshooting issues

## Activity Tracking

Keep track of changes to your test assets:

### Recent Test Runs

- View recent test runs in the project dashboard
- See who ran each test and when
- Access test results directly from the history

### Change History

- See when test suites and resources were last modified
- Track who made changes
- Identify which tests were recently added or updated
