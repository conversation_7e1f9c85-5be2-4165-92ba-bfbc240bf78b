---
sidebar_position: 1
---

# User Guide

The User Guide provides detailed information about all features of UrTest.

## Dashboard & Navigation

The dashboard is your starting point in UrTest. From here, you can:

- See an overview of all your projects
- View recent test runs
- Access quick statistics about your test automation
- Navigate to different sections of the application

## Project Management

Projects help you organize related test suites and resources. Learn how to:

- Create and configure projects
- Manage project settings
- Assign staff to projects
- View project metrics and reports

## Test Suite Management

Test suites are collections of test cases. This section covers:

- Creating and editing test suites
- Organizing test cases within suites
- Using tags for categorization
- Managing test suite versions

## Test Resources

Resources are reusable components for your test suites. Learn about:

- Creating and managing resource files
- Importing resources into test suites
- Best practices for resource organization

## Test Execution

This section covers running tests in UrTest:

- Running individual test suites
- Executing all tests in a project
- Viewing and interpreting test results
- Debugging failed tests

## Collaboration Features

Work efficiently with your team:

- Using comments to discuss test cases
- Assigning team members to projects
- Tracking changes to test suites and resources
