---
sidebar_position: 1
---

# Getting Started with UrTest

Welcome to UrTest! This guide will help you get started with the platform.

## Logging In

To access UrTest:

1. Navigate to your UrTest URL in your browser
2. Enter your username and password on the login screen
3. Click "Login" to access the platform

## User Interface Overview

After logging in, you'll see the main dashboard with these key elements:

- **Top Navigation Bar**: Access global functions like search and user settings
- **Sidebar**: Navigate between different sections of the application
- **Main Content Area**: View and interact with projects, test suites, and resources

## User Roles and Permissions

UrTest has different user roles with varying permissions:

- **Admin**: Full access to all features and settings
- **Manager**: Can create and manage projects and users
- **Staff**: Can create and edit test suites within assigned projects

Your role determines what actions you can perform in the system.

## Creating Your First Project

To create your first project:

1. Click on "Dashboard" in the sidebar
2. Click the "Create Project" button
3. Enter a name and description for your project
4. Click "Create" to save your new project

Now you're ready to start creating test suites and resources!
