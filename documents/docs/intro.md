---
sidebar_position: 1
---

# Welcome to UrTest

Welcome to the UrTest documentation! 

UrTest is a comprehensive test automation management platform that integrates with Robot Framework to help teams create, organize, and execute test cases efficiently.

## Quick Links

- [Introduction](introduction/index.md) - Learn about UrTest and its features
- [Getting Started](getting-started/index.md) - Get up and running quickly
- [User Guide](user-guide/index.md) - Detailed information about using UrTest
- [Robot Framework Integration](robot-framework/index.md) - Working with Robot Framework in UrTest
- [Administrative Tasks](admin-tasks/index.md) - User and project management
- [Troubleshooting & FAQ](troubleshooting/index.md) - Solutions to common problems

## Key Features

UrTest provides a complete solution for test automation teams:

- **Integrated Test Editor** with syntax highlighting and auto-completion
- **Test Suite Management** for organizing test cases
- **Resource Management** for reusable test components
- **Collaborative Features** like comments and staff assignment
- **Test Execution** capabilities with detailed reporting
- **AI Assistance** for test writing and best practices

## Getting Started

The quickest way to get started with UrTest is to:

1. [Log in to the platform](getting-started/index.md)
2. [Create your first project](getting-started/index.md#creating-your-first-project)
3. [Create a test suite](getting-started/first-test-suite.md)
4. [Run your first test](getting-started/first-test-suite.md#running-your-test-suite)

## Need Help?

If you can't find what you're looking for in the documentation:

- Use the search feature at the top of the page
- Check the [Troubleshooting & FAQ](troubleshooting/index.md) section
- Contact your system administrator for additional support
