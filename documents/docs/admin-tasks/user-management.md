---
sidebar_position: 2
---

# User Management

Properly managing user access is important for team collaboration in UrTest.

## Understanding User Roles

UrTest supports several user roles:

- **Administrators**: Full access to all projects and system settings
- **Managers**: Can create and manage projects and assign staff
- **Staff**: Can create and edit test cases within assigned projects

## Adding Users to Projects

To assign users to a project:

1. Navigate to the project
2. Click the "Manage Staff" button
3. The staff management dialog will open
4. Switch to the "Available Staff" tab
5. Click the "+" icon next to users you want to add
6. The users will appear in the "Assigned Staff" tab

## Removing Users from Projects

To remove a user from a project:

1. Navigate to the project
2. Click the "Manage Staff" button
3. In the "Assigned Staff" tab, find the user
4. Click the trash icon next to the user's name
5. Confirm the removal when prompted

:::caution
Removing a user from a project means they will no longer have access to any test suites or resources in that project.
:::

## Best Practices for User Management

- Assign users only to projects they need to work on
- Regularly review user assignments and remove unnecessary access
- Use the appropriate role for each user based on their responsibilities
- Consider creating separate projects for teams that work independently
