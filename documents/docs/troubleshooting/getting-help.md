---
sidebar_position: 4
---

# Getting Help

If you can't find a solution to your problem in the documentation, here are additional ways to get help.

## In-App Help

UrTest includes several built-in help features:

- **AI Assistant**: Available in the test editor to help with writing test cases
- **Tooltips**: Hover over UI elements to see brief explanations
- **Inline documentation**: The editor provides suggestions and documentation for Robot Framework keywords

## Contact Support

For issues that aren't resolved by the documentation:

1. Describe your issue in detail
2. Include any error messages you're seeing
3. Provide steps to reproduce the problem
4. Note what you've already tried

Send this information to your system administrator or support team.

## Additional Resources

Learn more about Robot Framework:

- [Official Robot Framework documentation](https://robotframework.org/robotframework/latest/RobotFrameworkUserGuide.html)
- [Robot Framework Standard Library documentation](https://robotframework.org/robotframework/#standard-libraries)
- [SeleniumLibrary documentation](https://robotframework.org/SeleniumLibrary/SeleniumLibrary.html)

## Reporting Bugs

If you believe you've found a bug in UrTest:

1. Document the steps to reproduce the issue
2. Capture screenshots if applicable
3. Note your browser and operating system
4. Report the issue to your administrator

## Feature Requests

If you have ideas for improving UrTest:

1. Clearly describe the feature you'd like to see
2. Explain the use case and benefits
3. Submit your request to your administrator or product team
