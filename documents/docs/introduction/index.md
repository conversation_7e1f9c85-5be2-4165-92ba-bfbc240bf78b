---
sidebar_position: 1
---

# Introduction to UrTest

## What is UrTest?

UrTest is a comprehensive test automation management platform that integrates with Robot Framework, providing teams with a powerful tool to create, organize, and execute test cases efficiently.

## Key Features

- **Integrated Test Editor**: Create and edit Robot Framework test scripts with syntax highlighting and auto-completion
- **Test Suite Management**: Organize test cases into logical test suites for better maintainability
- **Resource Management**: Create and manage reusable test resources
- **Collaborative Testing**: Work together with your team on test cases with comments and staff assignment
- **Test Execution**: Run tests directly from the platform and view detailed test results
- **AI Assistance**: Get help with test writing and best practices from the integrated AI assistant

## Core Concepts

Here are the main concepts you'll work with in UrTest:

### Projects

Projects help you organize related test suites and resources. Each project can be assigned to specific team members.

### Test Suites

Test suites are collections of test cases that share a common purpose or feature area.

### Test Resources

Resources are reusable components like keywords, variables, or settings that can be shared across test suites.

## Who Should Use UrTest?

UrTest is designed for:

- QA Engineers
- Test Automation Specialists
- Development Teams
- QA Managers

Get started with UrTest to streamline your test automation workflow!
