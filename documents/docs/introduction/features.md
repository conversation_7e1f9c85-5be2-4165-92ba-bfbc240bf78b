---
sidebar_position: 2
---

# Key Features

UrTest comes with a range of powerful features to help your team create, manage, and execute tests efficiently.

## Test Editor

The integrated Monaco editor provides a seamless experience for writing Robot Framework test scripts:

- **Syntax Highlighting**: Clearly distinguish keywords, variables, and comments
- **Auto-completion**: Get suggestions for Robot Framework keywords as you type
- **Error Detection**: Identify syntax errors before running your tests
- **Code Folding**: Collapse sections of your test scripts for better readability

## Collaboration Features

Work together with your team on test automation:

- **Comments**: Add comments to test suites to discuss implementation details
- **Staff Assignment**: Assign team members to specific projects
- **Activity Tracking**: See recent changes to test suites and resources

## Test Resource Management

Manage your test resources effectively:

- **Centralized Repository**: Store all test resources in one place
- **Dependency Management**: See which test suites use which resources
- **Reusability**: Create resources once and use them across multiple test suites

## Test Execution

Run your tests directly from the platform:

- **Single Test Execution**: Run individual test suites
- **Batch Execution**: Run all tests in a project
- **Real-time Feedback**: See test progress and results as tests run
- **Detailed Reports**: Access comprehensive test reports after execution

## AI Assistant

Get help when you need it:

- **Test Writing Assistance**: Get suggestions for writing effective test cases
- **Best Practices**: Learn about Robot Framework best practices
- **Quick Answers**: Ask questions about test automation concepts
