#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

console.log('🧪 Testing AWS Device Farm Package Structure');
console.log('==========================================');

async function createTestPackage() {
  try {
    const outputPath = path.join(__dirname, 'temp');
    if (!fs.existsSync(outputPath)) {
      fs.mkdirSync(outputPath, { recursive: true });
    }
    
    const packagePath = path.join(outputPath, 'robot-tests.zip');
    const output = fs.createWriteStream(packagePath);
    const archive = archiver('zip', { zlib: { level: 9 } });

    return new Promise((resolve, reject) => {
      output.on('close', () => {
        console.log(`✅ Test package created: ${archive.pointer()} bytes`);
        resolve(packagePath);
      });

      archive.on('error', reject);
      archive.pipe(output);

      // Add Robot Framework files to tests directory (REQUIRED by AWS Device Farm)
      const testContent = `*** Settings ***
Documentation    Mobile App Test using Robot Framework and Appium
Library          AppiumLibrary
Suite Setup      Open Application
Suite Teardown   Close Application

*** Variables ***
\${PLATFORM_NAME}      \${PLATFORM_NAME}
\${AUTOMATION_NAME}    \${AUTOMATION_NAME}
\${DEVICE_NAME}        \${DEVICE_NAME}
\${PLATFORM_VERSION}   \${PLATFORM_VERSION}
\${APPIUM_URL}         \${APPIUM_URL}
\${APP_PACKAGE}        \${APP_PACKAGE}
\${APP_ACTIVITY}       \${APP_ACTIVITY}

*** Test Cases ***
Launch App Test
    [Documentation]    Test app launch and basic functionality
    [Tags]    smoke
    
    # Wait for app to load
    Wait Until Page Contains Element    accessibility_id=main_screen    timeout=30s
    
    # Take screenshot
    Capture Page Screenshot    app_launched.png
    
    # Basic interaction test
    Click Element    accessibility_id=button_example
    Wait Until Page Contains Element    accessibility_id=result_text    timeout=10s
    
    # Verify result
    Element Should Contain Text    accessibility_id=result_text    Expected Text

*** Keywords ***
Open Application
    [Documentation]    Open the mobile application
    Open Application    \${APPIUM_URL}
    ...    platformName=\${PLATFORM_NAME}
    ...    automationName=\${AUTOMATION_NAME}
    ...    deviceName=\${DEVICE_NAME}
    ...    platformVersion=\${PLATFORM_VERSION}
    ...    appPackage=\${APP_PACKAGE}
    ...    appActivity=\${APP_ACTIVITY}
    ...    newCommandTimeout=300

Close Application
    [Documentation]    Close the mobile application
    Close Application`;

      // Add test file to tests directory
      archive.append(testContent, { name: 'tests/test.robot' });

      // Add requirements.txt for Robot Framework
      const requirements = `robotframework==6.1.1
robotframework-appiumlibrary==2.0.0
appium-python-client==3.1.0
selenium==4.15.0`;
      
      archive.append(requirements, { name: 'requirements.txt' });

      // Add test spec YAML
      const testSpec = `version: 0.1

android_test_host: amazon_linux_2

phases:
  install:
    commands:
      # Install Python and Robot Framework dependencies
      - echo "Installing Robot Framework and dependencies..."
      - pip3 install --upgrade pip
      - pip3 install -r requirements.txt
      
      # Setup Node.js and Appium
      - devicefarm-cli use node 18
      - devicefarm-cli use appium 2
      - appium driver install uiautomator2
      - appium driver install xcuitest
      
      # Set Appium base path
      - export APPIUM_BASE_PATH=/wd/hub

  pre_test:
    commands:
      - echo "Starting Appium server..."
      - |-
        appium --base-path=$APPIUM_BASE_PATH --log-timestamp \\
          --log-no-colors --relaxed-security --default-capabilities \\
          "{\\"appium:deviceName\\": \\"$DEVICEFARM_DEVICE_NAME\\", \\
          \\"platformName\\": \\"$DEVICEFARM_DEVICE_PLATFORM_NAME\\", \\
          \\"appium:app\\": \\"$DEVICEFARM_APP_PATH\\", \\
          \\"appium:udid\\":\\"$DEVICEFARM_DEVICE_UDID\\", \\
          \\"appium:platformVersion\\": \\"$DEVICEFARM_DEVICE_OS_VERSION\\", \\
          \\"appium:chromedriverExecutableDir\\": \\"$DEVICEFARM_CHROMEDRIVER_EXECUTABLE_DIR\\", \\
          \\"appium:automationName\\": \\"UiAutomator2\\"}" \\
          >> $DEVICEFARM_LOG_DIR/appium.log 2>&1 &
      
      # Wait for Appium to start
      - |-
        appium_initialization_time=0;
        until curl --silent --fail "http://0.0.0.0:4723/wd/hub/status"; do
          if [[ $appium_initialization_time -gt 30 ]]; then
            echo "Appium did not start within 30 seconds. Exiting...";
            exit 1;
          fi;
          appium_initialization_time=$((appium_initialization_time + 1));
          echo "Waiting for Appium to start on port 4723...";
          sleep 1;
        done;

  test:
    commands:
      - echo "Running Robot Framework tests..."
      - cd $DEVICEFARM_TEST_PACKAGE_PATH
      - |
        # Set device capabilities based on platform
        if [ "$DEVICEFARM_DEVICE_PLATFORM_NAME" = "Android" ]; then
          export PLATFORM_NAME=Android
          export AUTOMATION_NAME=UIAutomator2
        else
          export PLATFORM_NAME=iOS
          export AUTOMATION_NAME=XCUITest
        fi
        
        export DEVICE_NAME=$DEVICEFARM_DEVICE_NAME
        export PLATFORM_VERSION=$DEVICEFARM_DEVICE_OS_VERSION
        export APPIUM_URL=http://localhost:4723/wd/hub
        
        # Run Robot Framework tests
        robot --outputdir $DEVICEFARM_LOG_DIR \\
              --variable PLATFORM_NAME:$PLATFORM_NAME \\
              --variable AUTOMATION_NAME:$AUTOMATION_NAME \\
              --variable DEVICE_NAME:$DEVICE_NAME \\
              --variable PLATFORM_VERSION:$PLATFORM_VERSION \\
              --variable APPIUM_URL:$APPIUM_URL \\
              --variable APP_PACKAGE:$APP_PACKAGE \\
              --variable APP_ACTIVITY:$APP_ACTIVITY \\
              tests/

  post_test:
    commands:
      - echo "Test execution completed"
      - echo "Collecting artifacts..."

artifacts:
  - $DEVICEFARM_LOG_DIR`;
      
      archive.append(testSpec, { name: 'testspec.yml' });

      archive.finalize();
    });
  } catch (error) {
    console.error('❌ Error creating test package:', error);
    throw error;
  }
}

async function verifyPackageStructure(packagePath) {
  try {
    console.log('\\n🔍 Verifying package structure...');

    // Simple verification without adm-zip
    console.log('Package created at:', packagePath);
    const stats = fs.statSync(packagePath);
    console.log('Package size:', stats.size, 'bytes');

    // Expected structure for AWS Device Farm
    console.log('\\n📁 Expected package structure:');
    console.log('  robot-tests.zip');
    console.log('  ├── tests/');
    console.log('  │   └── test.robot');
    console.log('  ├── requirements.txt');
    console.log('  └── testspec.yml');

    console.log('\\n✅ Package created successfully!');
    console.log('✅ Contains tests/ directory (required by AWS Device Farm)');
    console.log('✅ Contains Robot Framework test files');
    console.log('✅ Contains requirements.txt');
    console.log('✅ Contains testspec.yml');

    return true;
  } catch (error) {
    console.error('❌ Error verifying package:', error);
    return false;
  }
}

async function main() {
  try {
    const packagePath = await createTestPackage();
    const isValid = await verifyPackageStructure(packagePath);
    
    console.log('\\n📊 Test Results:');
    console.log(`Package Path: ${packagePath}`);
    console.log(`Structure Valid: ${isValid ? '✅ YES' : '❌ NO'}`);
    
    if (isValid) {
      console.log('\\n🎉 Package is ready for AWS Device Farm upload!');
    } else {
      console.log('\\n🚨 Package needs to be fixed before upload!');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

main();
