name: Deploy Dev
on:
  push:
    branches:
      - "dev/deploy"
  pull_request:
    branches:
      - "dev/deploy"

env:
  ECR_REPOSITORY: 763240221275.dkr.ecr.ap-southeast-1.amazonaws.com
  COMMIT_ID: ${{ github.sha }}
  ENVIRONMENT: urtest

  PROJECT: urtest

  GIT_BRANCH: ${{ github.ref_name }}

jobs:
  check_changes:
    runs-on: ubuntu-latest
    outputs:
      changed_folders: ${{ steps.changes.outputs.changed_folders }}
      commit_id_short: ${{ steps.set_commit_id.outputs.commit_id_short }}
    steps:
      - name: Fetch code from repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Print commit information
        run: |
          echo "Running pipeline for commit: ${{ github.sha }}"
          echo "Run time: $(date)"

      - name: Shorten COMMIT_ID to 7 characters
        id: set_commit_id
        run: |
          COMMIT_ID_SHORT=$(echo "${{ github.sha }}" | cut -c 1-7)
          echo "commit_id_short=$COMMIT_ID_SHORT" >> $GITHUB_OUTPUT

      - name: Get list of changed files
        id: changed-files
        uses: tj-actions/changed-files@v46

      - name: Check changed files
        run: |
          echo "List of changed files: ${{ steps.changed-files.outputs.all_changed_files }}"

      - name: Identify changed folders
        id: changes
        run: |
          CHANGED_FOLDERS=()

          IFS=' ' read -ra FILES <<< "${{ steps.changed-files.outputs.all_changed_files }}"

          for folder in backend documents frontend jira-bridge keyword-extractor runner; do
            FOLDER_CHANGED=false
            
            for file in "${FILES[@]}"; do
              if [[ "$file" == "$folder/"* ]]; then
                FOLDER_CHANGED=true
                break
              fi
            done
            
            if [ "$FOLDER_CHANGED" = true ]; then
              CHANGED_FOLDERS+=("$folder")
              echo "Detected changes in folder: $folder"
            fi
          done

          if [ ${#CHANGED_FOLDERS[@]} -eq 0 ]; then
            echo "changed_folders=[]" >> $GITHUB_OUTPUT
            echo "No folders changed."
          else
            JSON_ARRAY="["
            for i in "${!CHANGED_FOLDERS[@]}"; do
              if [ $i -gt 0 ]; then
                JSON_ARRAY="$JSON_ARRAY,"
              fi
              JSON_ARRAY="$JSON_ARRAY \"${CHANGED_FOLDERS[$i]}\""
            done
            JSON_ARRAY="$JSON_ARRAY ]"
            echo "DEBUG: CHANGED_FOLDERS=${CHANGED_FOLDERS[*]}"
            echo "DEBUG: JSON_ARRAY=$JSON_ARRAY"
            echo "changed_folders=$JSON_ARRAY" >> $GITHUB_OUTPUT
            echo "Changed folders: ${CHANGED_FOLDERS[@]}"
          fi

  build_services:
    needs: check_changes
    if: needs.check_changes.outputs.changed_folders != '[]'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: ${{ fromJSON(needs.check_changes.outputs.changed_folders) }}
    name: Build ${{ matrix.service }}
    env:
      DOMAIN_NAME: ${{ matrix.service }}.urbox.dev
      COMMIT_ID: ${{ needs.check_changes.outputs.commit_id_short }}
    steps:
      - name: Fetch code for build
        uses: actions/checkout@v4

      - name: Install Bun for backend
        if: matrix.service == 'backend'
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Install Node.js for documents
        if: matrix.service == 'documents'
        uses: actions/setup-node@v4
        with:
          node-version: "18"

      - name: Install Node.js for frontend
        if: matrix.service == 'frontend'
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Install Node.js for jira-bridge
        if: matrix.service == 'jira-bridge'
        uses: actions/setup-node@v4
        with:
          node-version: "18"

      - name: Install Node.js for keyword-extractor
        if: matrix.service == 'keyword-extractor'
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Install Node.js for runner
        if: matrix.service == 'runner'
        uses: actions/setup-node@v4
        with:
          node-version: "18"

      - name: Install Python for runner
        if: matrix.service == 'runner'
        uses: actions/setup-python@v5
        with:
          python-version: "3.x"

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_DEV }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Log in to ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build and push image to ECR
        run: |
          cd ${{ matrix.service }}
          echo "Starting image build for ${{ matrix.service }}..."
          docker build --platform=linux/amd64 -t ${{ env.ECR_REPOSITORY }}/${{ env.ENVIRONMENT }}-${{ matrix.service }}:${{ env.COMMIT_ID }} -f Dockerfile .
          if [ $? -ne 0 ]; then
            echo "Error: Image build failed for ${{ matrix.service }}"
            exit 1
          fi
          echo "Build successful: ${{ env.ECR_REPOSITORY }}/${{ matrix.service }}:${{ env.COMMIT_ID }}"
          echo "Starting image push to ECR..."
          docker push ${{ env.ECR_REPOSITORY }}/${{ env.ENVIRONMENT }}-${{ matrix.service }}:${{ env.COMMIT_ID }}
          if [ $? -ne 0 ]; then
            echo "Error: Image push failed for ${{ matrix.service }}"
            exit 1
          fi
          echo "Image push successful: ${{ env.ECR_REPOSITORY }}/${{ matrix.service }}:${{ env.COMMIT_ID }}"

  update_and_deploy:
    needs: [build_services, check_changes]
    runs-on: [self-hosted, Linux, X64, dev]
    environment: develop
    strategy:
      matrix:
        service: ${{ fromJSON(needs.check_changes.outputs.changed_folders) }}
    name: Update and Deploy ${{ matrix.service }}
    env:
      DOMAIN_NAME: ${{ matrix.service }}.urbox.dev
      COMMIT_ID: ${{ needs.check_changes.outputs.commit_id_short }}
    steps:
      - name: Fetch code for update
        uses: actions/checkout@v4

      - name: Update image tag in values.yaml
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Auto Deploy"
          rm -rf urbox-deployment
          git clone ${{ secrets.GITOPS_REPO }}
          cd urbox-deployment/urbox-services/${{ env.ENVIRONMENT }}/${{ matrix.service }}

          # Retry mechanism for git operations
          for i in {1..5}; do
            echo "Attempt $i to update and push..."
            
            # Pull latest changes first
            git pull origin main
            
            # Update the values.yaml
            sed -i "/image:/,/tag:/ s|tag:.*|tag: \"${{ env.COMMIT_ID }}\"|g" values.yaml
            
            # Check if there are any changes
            if git diff --quiet; then
              echo "No changes to commit"
              cd ../../..
              rm -rf urbox-deployment
              exit 0
            fi
            
            # Add and commit changes
            git add .
            git commit -m "Update ${{ matrix.service }} - https://github.com/${{ github.repository }}/commit/${{ github.sha }}" --allow-empty
            
            # Try to push
            if git push origin main; then
              echo "Successfully pushed on attempt $i"
              break
            else
              echo "Push failed on attempt $i, retrying..."
              if [ $i -eq 5 ]; then
                echo "Failed to push after 5 attempts"
                cd ../../..
                rm -rf urbox-deployment
                exit 1
              fi
              
              # Reset to clean state for next attempt
              git reset --hard HEAD~1
              sleep 2
            fi
          done

          cd ../../..
          rm -rf urbox-deployment

      - name: Deploy to EKS
        run: |
          argocd login ${{ secrets.ARGOCD_URL_DEV }} --username ${{ secrets.ARGOCD_USER_DEV }} --password ${{ secrets.ARGOCD_PASS_DEV }} --skip-test-tls --grpc-web
          argocd app get ${{ env.ENVIRONMENT }}-${{ matrix.service }} --server ${{ secrets.ARGOCD_URL_DEV }} --hard-refresh
