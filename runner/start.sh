#!/bin/sh

echo "Starting Xvfb..."
Xvfb :99 -screen 0 1280x1024x24 -ac &
XVFB_PID=$!

echo "Waiting for Xvfb to start..."
for i in 1 2 3 4 5 6 7 8 9 10; do
  if xdpyinfo -display :99 > /dev/null 2>&1; then
    echo "Xvfb started successfully."
    break
  fi
  sleep 1
done

if ! xdpyinfo -display :99 > /dev/null 2>&1; then
  echo "Xvfb failed to start after 10 seconds."
  exit 1
fi

export DISPLAY=:99

echo "Chrome version: $(chromium-browser --version)"
echo "ChromeDriver version: $(chromedriver --version)"

echo "Starting Appium server..."
appium --address 0.0.0.0 --port 4723 --relaxed-security &

echo "Starting Node.js application..."
node app.js