FROM node:18-alpine AS builder

WORKDIR /app

COPY package*.json ./

RUN npm ci

COPY . .

FROM node:18-alpine

RUN apk add --no-cache git openssh python3 py3-pip chromium chromium-chromedriver xvfb curl unzip xdpyinfo \
    openjdk11-jre android-tools bash

ENV CHROME_BIN=/usr/bin/chromium-browser \
    CHROME_PATH=/usr/lib/chromium/ \
    CHROMEDRIVER_PATH=/usr/bin/chromedriver \
    DISPLAY=:99 \
    PATH="/usr/bin:/usr/local/bin:${PATH}"

RUN python3 -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

RUN pip3 install --no-cache-dir \
    selenium==4.8.3 \
    robotframework==6.1.1 \
    robotframework-requests \
    robotframework-seleniumlibrary==6.1.0 \
    robotframework-databaselibrary \
    psycopg2-binary \
    jsonschema \
    robotframework-faker \
    robotframework-appiumlibrary \
    appium-python-client

# Install Appium
RUN npm install -g appium@2.19.0

# Install UIAutomator2 driver
RUN appium driver install uiautomator2

RUN chromium-browser --version && \
    chromedriver --version && \
    ls -la /usr/bin/chromedriver

RUN addgroup -S appgroup && adduser -S appuser -G appgroup

WORKDIR /app

COPY --from=builder /app/package*.json ./
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/src ./src
COPY --from=builder /app/app.js ./
COPY .env /app/.env

COPY start.sh /app/start.sh

RUN mkdir -p tests/tests && chown -R appuser:appgroup /app
RUN chmod 755 /usr/bin/chromium-browser && \
    chmod +x /usr/bin/chromedriver && \
    chmod +x /app/start.sh

ENV NODE_ENV=production \
    PORT=3000 \
    PYTHONPATH=/app
    
USER root

EXPOSE 3000 4723

CMD ["sh", "/app/start.sh"]
