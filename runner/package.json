{"name": "urtest-runner", "version": "1.0.0", "description": "Service for running Robot Framework tests", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["robot", "test", "expressjs"], "author": "dongtran", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "fs-extra": "^11.1.1", "helmet": "^7.0.0", "minio": "^7.1.1", "morgan": "^1.10.0", "simple-git": "^3.19.1", "winston": "^3.10.0"}, "devDependencies": {"nodemon": "^3.0.1"}}