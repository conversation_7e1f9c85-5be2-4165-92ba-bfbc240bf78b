FROM oven/bun:latest

WORKDIR /app

COPY . .

RUN echo "=== Checking .env file ===" && \
  if [ -f .env ]; then \
  echo ".env file exists, content:" && \
  cat .env; \
  else \
  echo ".env file does not exist"; \
  fi

RUN echo "=== Environment variables before setting ===" && \
  echo "SERVER_PORT: $SERVER_PORT" && \
  echo "BACKEND_NODE_ENV: $BACKEND_NODE_ENV"

ENV BACKEND_NODE_ENV=production
ENV SERVER_PORT=3020

RUN echo "=== Environment variables after setting ===" && \
  echo "SERVER_PORT: $SERVER_PORT" && \
  echo "BACKEND_NODE_ENV: $BACKEND_NODE_ENV"

RUN echo "=== Testing Bun.env ===" && \
  bun -e "console.log('Bun.env.SERVER_PORT:', Bun.env.SERVER_PORT); console.log('typeof:', typeof Bun.env.SERVER_PORT);"

RUN apt update && apt install -y wget
RUN bun install --frozen-lockfile

EXPOSE 3020

HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
  CMD wget -qO- http://localhost:3020/health || exit 1

CMD ["bun", "run", "start"]
