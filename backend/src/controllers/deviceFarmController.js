const awsDeviceFarmService = require('../services/awsDeviceFarmService');
const fs = require('fs-extra');
const path = require('path');

class DeviceFarmController {
  /**
   * Get available device pools
   */
  async getDevicePools(req, res) {
    try {
      console.log('📱 Getting AWS Device Farm device pools...');
      const devicePools = await awsDeviceFarmService.getDevicePools();
      
      res.json({
        success: true,
        devicePools,
        count: devicePools.length
      });
    } catch (error) {
      console.error('Error getting device pools:', error);
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * Get devices in a specific pool
   */
  async getDevicesInPool(req, res) {
    try {
      const { devicePoolArn } = req.params;
      
      if (!devicePoolArn) {
        return res.status(400).json({
          success: false,
          message: 'devicePoolArn is required'
        });
      }

      console.log(`📱 Getting devices in pool: ${devicePoolArn}`);
      const devicePool = await awsDeviceFarmService.getDevicesInPool(devicePoolArn);
      
      res.json({
        success: true,
        devicePool
      });
    } catch (error) {
      console.error('Error getting devices in pool:', error);
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * Run Robot Framework tests on Device Farm
   */
  async runRobotTests(req, res) {
    try {
      const { 
        testFiles, 
        appFile, 
        runName, 
        devicePoolArn,
      } = req.body;
      
      if (!testFiles || !testFiles.length) {
        return res.status(400).json({
          success: false,
          message: 'testFiles are required'
        });
      }

      if (!appFile) {
        return res.status(400).json({
          success: false,
          message: 'appFile is required'
        });
      }
      
      // Create temporary directory for uploads
      const tempDir = path.join(__dirname, '../../temp', Date.now().toString());
      await fs.ensureDir(tempDir);

      try {
        // 1. Create test package
        const testPackagePath = await awsDeviceFarmService.createRobotTestPackage(
          testFiles, 
          tempDir
        );

        // 2. Upload test package
        const testPackageArn = await awsDeviceFarmService.uploadFile(
          testPackagePath,
          'APPIUM_PYTHON_TEST_PACKAGE',
          `${runName}-tests.zip`
        );

        // 3. Upload app (if provided as file path)
        let appArn;
        if (appFile.path) {
          appArn = await awsDeviceFarmService.uploadFile(
            appFile.path,
            appFile.platform === 'ios' ? 'IOS_APP' : 'ANDROID_APP',
            appFile.name
          );
        } else {
          appArn = appFile.arn; // Use existing app ARN
        }

        // 4. Upload test spec
        const testSpecContent = awsDeviceFarmService.generateRobotTestSpec();
        const testSpecPath = path.join(tempDir, 'testspec.yml');
        await fs.writeFile(testSpecPath, testSpecContent);
        
        const testSpecArn = await awsDeviceFarmService.uploadFile(
          testSpecPath,
          'APPIUM_PYTHON_TEST_SPEC',
          `${runName}-testspec.yml`
        );

        // 5. Schedule test run
        const runArn = await awsDeviceFarmService.scheduleRun(
          appArn,
          testPackageArn,
          runName,
          devicePoolArn,
          testSpecArn
        );

        // Return run information
        res.json({
          success: true,
          runArn,
          message: 'Test run scheduled successfully',
          testPackageArn,
          appArn,
          testSpecArn
        });

      } finally {
        // Cleanup temp directory
        await fs.remove(tempDir);
      }

    } catch (error) {
      console.error('Error running Robot tests:', error);
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * Get test run status
   */
  async getRunStatus(req, res) {
    try {
      const { runArn } = req.params;
      
      if (!runArn) {
        return res.status(400).json({
          success: false,
          message: 'runArn is required'
        });
      }
      
      // Monitor run (single check)
      const result = await awsDeviceFarmService.monitorRun(runArn, null);
      
      res.json({
        success: true,
        ...result
      });
    } catch (error) {
      console.error('Error getting run status:', error);
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * Get test results
   */
  async getTestResults(req, res) {
    try {
      const { runArn } = req.params;
      
      if (!runArn) {
        return res.status(400).json({
          success: false,
          message: 'runArn is required'
        });
      }

      const results = await awsDeviceFarmService.getTestResults(runArn);
      
      res.json({
        success: true,
        results
      });
    } catch (error) {
      console.error('Error getting test results:', error);
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * Monitor test run with real-time updates
   */
  async monitorRun(req, res) {
    try {
      const { runArn } = req.params;
      
      if (!runArn) {
        return res.status(400).json({
          success: false,
          message: 'runArn is required'
        });
      }
      
      // Set up SSE for real-time updates
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*'
      });

      // Monitor run with progress callback
      const result = await awsDeviceFarmService.monitorRun(runArn, (progress) => {
        res.write(`data: ${JSON.stringify(progress)}\n\n`);
      });

      // Send final result
      res.write(`data: ${JSON.stringify({ type: 'completed', ...result })}\n\n`);
      res.end();

    } catch (error) {
      console.error('Error monitoring run:', error);
      res.write(`data: ${JSON.stringify({ type: 'error', message: error.message })}\n\n`);
      res.end();
    }
  }

  /**
   * Create Robot Framework test template
   */
  async getTestTemplate(req, res) {
    try {
      const { platform = 'android' } = req.query;
      
      const template = `*** Settings ***
Documentation    Mobile App Test using Robot Framework and Appium
Library          AppiumLibrary
Suite Setup      Open Application
Suite Teardown   Close Application

*** Variables ***
\${PLATFORM_NAME}      \${PLATFORM_NAME}
\${AUTOMATION_NAME}    \${AUTOMATION_NAME}
\${DEVICE_NAME}        \${DEVICE_NAME}
\${PLATFORM_VERSION}   \${PLATFORM_VERSION}
\${APPIUM_URL}         \${APPIUM_URL}
\${APP_PACKAGE}        \${APP_PACKAGE}
\${APP_ACTIVITY}       \${APP_ACTIVITY}

*** Test Cases ***
Launch App Test
    [Documentation]    Test app launch and basic functionality
    [Tags]    smoke
    
    # Wait for app to load
    Wait Until Page Contains Element    accessibility_id=main_screen    timeout=30s
    
    # Take screenshot
    Capture Page Screenshot    app_launched.png
    
    # Basic interaction test
    Click Element    accessibility_id=button_example
    Wait Until Page Contains Element    accessibility_id=result_text    timeout=10s
    
    # Verify result
    Element Should Contain Text    accessibility_id=result_text    Expected Text
    
    # Take final screenshot
    Capture Page Screenshot    test_completed.png

Navigation Test
    [Documentation]    Test app navigation
    [Tags]    navigation
    
    # Navigate through app screens
    Click Element    accessibility_id=menu_button
    Wait Until Page Contains Element    accessibility_id=menu_screen    timeout=10s
    
    Click Element    accessibility_id=settings_option
    Wait Until Page Contains Element    accessibility_id=settings_screen    timeout=10s
    
    # Go back
    Go Back
    Wait Until Page Contains Element    accessibility_id=menu_screen    timeout=10s

*** Keywords ***
Open Application
    [Documentation]    Open the mobile application
    Open Application    \${APPIUM_URL}
    ...    platformName=\${PLATFORM_NAME}
    ...    automationName=\${AUTOMATION_NAME}
    ...    deviceName=\${DEVICE_NAME}
    ...    platformVersion=\${PLATFORM_VERSION}
    ...    appPackage=\${APP_PACKAGE}
    ...    appActivity=\${APP_ACTIVITY}
    ...    newCommandTimeout=300

Close Application
    [Documentation]    Close the mobile application
    Close Application`;

      res.json({
        success: true,
        template,
        platform,
        description: 'Robot Framework test template for AWS Device Farm'
      });
    } catch (error) {
      console.error('Error getting test template:', error);
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }
}

module.exports = new DeviceFarmController();
