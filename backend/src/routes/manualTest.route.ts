import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import db from 'db/db';
import { ManualTestCaseTable, BugTable, ManualTestHistoryTable } from 'db/schema';
import { eq, and, isNull, desc as drizzleDesc, count } from 'drizzle-orm';
import dayjs from 'dayjs';
import fromNow from 'dayjs/plugin/relativeTime';
import { ulid } from 'ulid';
import * as ManualTestCaseSchema from 'lib/Zod/ManualTestCaseSchema';
import * as BugSchema from 'lib/Zod/BugSchema';
import CheckPermission, { ROLES } from '@middlewars/CheckPermission';
import CheckProjectAccess from '@middlewars/CheckProjectAccess';
import { logActivity, ACTIVITY_TYPES } from '../lib/ActivityLogger';
import { logManualTestHistory, ACTION_TYPES } from '../lib/ManualTestHistoryLogger';
import { z } from 'zod';
import xlsx from 'xlsx';
import { mergeColumnByAoaData } from 'utils/handleMergeColumn';
import { compact, isEmpty } from 'lodash';
import { uploadFileToS3, deleteFileFromS3, type UploadedFile } from '../lib/S3/s3Client';

dayjs.extend(fromNow);

const ManualTestRoute = new Hono();

ManualTestRoute.get(
  '/stats',
  CheckPermission([ROLES.ADMIN, ROLES.MANAGER, ROLES.STAFF]),
  CheckProjectAccess(),
  zValidator('query', ManualTestCaseSchema.schemaForGetTestCases.pick({ projectId: true })),
  async (ctx) => {
    const { projectId } = ctx.req.valid('query');

    const testCases = await db.query.ManualTestCaseTable.findMany({
      where: (clm, { eq, and, isNull }) => and(eq(clm.projectId, projectId), isNull(clm.deletedAt)),
    });

    const stats = {
      totalTestCases: testCases.length,
      passed: testCases.filter((tc) => tc.status === 'passed').length,
      failed: testCases.filter((tc) => tc.status === 'failed').length,
      new: testCases.filter((tc) => tc.status === 'new').length,
      activeBugs: testCases.filter((tc) => tc.bugStatusType === 'bug').length,
      progress:
        testCases.length > 0
          ? Math.round(
              (testCases.filter((tc) => tc.status === 'passed').length / testCases.length) * 100
            )
          : 0,
    };

    return ctx.json(stats);
  }
);

ManualTestRoute.get(
  '/test-cases',
  CheckPermission([ROLES.ADMIN, ROLES.MANAGER, ROLES.STAFF]),
  CheckProjectAccess(),
  zValidator('query', ManualTestCaseSchema.schemaForGetTestCases),
  async (ctx) => {
    const query = ctx.req.valid('query');

    let conditions: any[] = [
      isNull(ManualTestCaseTable.deletedAt),
      eq(ManualTestCaseTable.projectId, query.projectId),
    ];

    if (query.status && query.status !== 'all') {
      const statusMap = {
        new: 'new',
        passed: 'passed',
        failed: 'failed',
      };
      conditions.push(eq(ManualTestCaseTable.status, statusMap[query.status] as any));
    }

    if (query.assignedTo) {
      conditions.push(eq(ManualTestCaseTable.assignedToEmail, query.assignedTo));
    }

    if (query.category) {
      conditions.push(eq(ManualTestCaseTable.category, query.category as any));
    }

    if (query.priority) {
      conditions.push(eq(ManualTestCaseTable.priority, query.priority as any));
    }

    const testCases = await db.query.ManualTestCaseTable.findMany({
      where: and(...conditions),
      orderBy: (clm, { desc }) => desc(clm.id),
    });

    const transformedTestCases = testCases.map((tc) => ({
      id: tc.id,
      name: tc.name,
      category: getCategoryLabel(tc.category),
      priority: tc.priority,
      assignedTo: tc.assignedToEmail
        ? {
            name: tc.assignedTo || tc.assignedToEmail.split('@')[0],
            email: tc.assignedToEmail,
            avatar: null,
          }
        : null,
      status: tc.status,
      bugStatus: {
        type: tc.bugStatusType,
        reporter: tc.bugReporter,
        message: tc.bugMessage || getBugStatusMessage(tc.bugStatusType),
      },
      lastUpdated: tc.updatedAt
        ? dayjs(tc.updatedAt).fromNow()
        : tc.createdAt
          ? dayjs(tc.createdAt).fromNow()
          : 'Never',
      estimatedTime: tc.estimatedTime,
      dueDate: tc.dueDate,
    }));

    return ctx.json(transformedTestCases);
  }
);

ManualTestRoute.get(
  '/test-cases/:id',
  CheckPermission([ROLES.ADMIN, ROLES.MANAGER, ROLES.STAFF]),
  CheckProjectAccess(),
  zValidator('param', ManualTestCaseSchema.schemaForIdParam),
  async (ctx) => {
    const { id } = ctx.req.valid('param');

    const testCase = await db.query.ManualTestCaseTable.findFirst({
      where: (clm, { eq, and, isNull }) => and(eq(clm.id, id), isNull(clm.deletedAt)),
    });

    if (!testCase) {
      return ctx.json({ message: 'Test case not found' }, 404);
    }

    const responseTc = {
      ...testCase,
      assignedTo: testCase.assignedToEmail,
    };

    return ctx.json(responseTc);
  }
);

ManualTestRoute.post(
  '/upload-bug-file',
  CheckPermission([ROLES.ADMIN, ROLES.MANAGER, ROLES.STAFF]),
  async (ctx) => {
    try {
      const formData = await ctx.req.formData();
      const file = formData.get('file') as File;
      const user = ctx.get('user');

      if (!file) {
        return ctx.json({ message: 'No file uploaded' }, 400);
      }

      if (file.size > 10 * 1024 * 1024) {
        return ctx.json({ message: 'File size exceeds 10MB limit' }, 400);
      }

      const allowedTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'image/svg+xml',
        'application/pdf',
        'text/plain',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      ];

      if (!allowedTypes.includes(file.type)) {
        return ctx.json({ message: 'File type not allowed' }, 400);
      }

      const uploadedFile = await uploadFileToS3(file, 'bugs');

      const projectId = formData.get('projectId') as string;
      const testCaseId = formData.get('testCaseId') as string;

      if (projectId) {
        await logManualTestHistory(
          ACTION_TYPES.UPLOAD_FILE,
          'FILE',
          uploadedFile.id,
          projectId,
          user.email,
          `Uploaded file "${uploadedFile.originalName}" for bug attachment`,
          {
            fileName: uploadedFile.originalName,
            fileSize: uploadedFile.size,
            mimeType: uploadedFile.mimeType,
          },
          {
            uploadedFile: uploadedFile,
          },
          testCaseId
        );
      }

      return ctx.json({
        message: 'File uploaded successfully',
        file: uploadedFile,
      });
    } catch (error) {
      console.error('Error uploading file:', error);
      return ctx.json({ message: 'Failed to upload file' }, 500);
    }
  }
);

ManualTestRoute.delete(
  '/delete-bug-file/:fileName',
  CheckPermission([ROLES.ADMIN, ROLES.MANAGER, ROLES.STAFF]),
  zValidator('param', z.object({ fileName: z.string() })),
  async (ctx) => {
    try {
      const { fileName } = ctx.req.valid('param');
      const user = ctx.get('user');

      await deleteFileFromS3(fileName, 'bugs');

      const projectId = ctx.req.query('projectId');
      const testCaseId = ctx.req.query('testCaseId');

      if (projectId) {
        await logManualTestHistory(
          ACTION_TYPES.DELETE_FILE,
          'FILE',
          fileName,
          projectId,
          user.email,
          `Deleted file "${fileName}"`,
          {
            fileName: fileName,
          },
          {},
          testCaseId
        );
      }

      return ctx.json({ message: 'File deleted successfully' });
    } catch (error) {
      console.error('Error deleting file:', error);
      return ctx.json({ message: 'Failed to delete file' }, 500);
    }
  }
);

ManualTestRoute.post(
  '/create-test-case-by-uploading',
  zValidator(
    'form',
    z.object({
      file: z.any(),
      projectId: z.string().ulid(),
    })
  ),
  async (ctx) => {
    const user = ctx.get('user');
    const formData = ctx.req.valid('form');
    const file = formData.file;
    let buffer: Buffer;
    if (file instanceof Blob) {
      buffer = Buffer.from(await file.arrayBuffer());
    } else if (file && file.data) buffer = file.data;
    else buffer = Buffer.from(file);

    const workbook = xlsx.read(buffer, { type: 'buffer' });

    const sheetName = workbook.SheetNames[0];
    if (sheetName !== 'Danh sách Testcase') {
      return ctx.json({ message: 'Tên sheet không hợp lệ' }, 400);
    }
    const sheet = workbook.Sheets[sheetName];

    const merges = sheet['!merges'];

    const aoaData: any[] = xlsx.utils.sheet_to_json(sheet, { header: 1, blankrows: false });
    if (merges && merges.length > 0) {
      mergeColumnByAoaData(aoaData, merges);
    }

    const data: (typeof ManualTestCaseTable.$inferInsert)[] = [];
    for (let i = 0; i < aoaData.length; i++) {
      if ([0, 1, 2].includes(i)) continue;
      const row = aoaData[i];
      const mainCategory = row[1];
      if (isEmpty(row) && row.length <= 0) continue;
      else if (!mainCategory || !String(mainCategory).trim()) continue;
      const testCaseName = compact([row[1], row[2], row[3]]).join(' - ');
      const assignedToEmail = row[12] || user.email;

      data.push({
        dataTest: row[6],
        inputDescription: row[8],
        desiredResults: row[9],
        id: ulid(),
        category: row[5],
        status: 'new',
        createdAt: dayjs().toISOString(),
        createdBy: user.email,
        name: testCaseName,
        projectId: formData.projectId,
        assignedTo: assignedToEmail.split('@')[0],
        assignedToEmail,
        bugMessage: null,
        bugReporter: null,
        bugStatusType: null,
        dueDate: null,
        estimatedTime: 15,
        executionHistory: null,
        notes: '',
        params: {},
        priority: 'Medium',
      });
    }

    const insertedData = await db.insert(ManualTestCaseTable).values(data).returning();

    await logManualTestHistory(
      ACTION_TYPES.BULK_CREATE_TEST_CASES,
      'PROJECT',
      formData.projectId,
      formData.projectId,
      user.email,
      `Bulk created ${data.length} test cases from uploaded file`,
      {
        fileName: file?.name || 'uploaded_file.xlsx',
        testCaseCount: data.length,
        testCaseNames: data.slice(0, 5).map((tc) => tc.name),
      },
      {
        uploadMethod: 'excel_file',
        totalTestCases: data.length,
        fileSize: buffer.length,
        testCaseIds: insertedData.map((tc) => tc.id),
      }
    );

    return ctx.json({ message: 'ok' });
  }
);

ManualTestRoute.post(
  '/test-cases',
  CheckPermission([ROLES.ADMIN, ROLES.MANAGER, ROLES.STAFF]),
  CheckProjectAccess(),
  zValidator('json', ManualTestCaseSchema.schemaForCreateTestCase),
  async (ctx) => {
    const body = ctx.req.valid('json');
    const user = ctx.get('user');

    const project = await db.query.ProjectTable.findFirst({
      where: (clm, { eq, and, isNull }) => and(eq(clm.id, body.projectId), isNull(clm.deletedAt)),
    });

    if (!project) {
      return ctx.json({ message: 'Project not found' }, 404);
    }

    const testCase = await db
      .insert(ManualTestCaseTable)
      .values({
        id: ulid(),
        projectId: body.projectId,
        name: body.name,
        category: body.category,
        priority: body.priority || 'Medium',
        estimatedTime: body.estimatedTime,
        assignedTo: body.assignedTo ? body.assignedTo.split('@')[0] : null,
        assignedToEmail: body.assignedTo,
        dueDate: body.dueDate,
        status: 'new',
        dataTest: body.dataTest || '',
        inputDescription: body.inputDescription || '',
        desiredResults: body.desiredResults || '',
        createdAt: dayjs().toISOString(),
        createdBy: user.email,
      })
      .returning()
      .then((res) => res[0]);

    await logActivity(
      ACTIVITY_TYPES.MANUAL_TEST_CASE_CREATED,
      body.projectId,
      user.email,
      `Created manual test case "${body.name}"`,
      testCase.id,
      'manual_test_case'
    );

    await logManualTestHistory(
      ACTION_TYPES.CREATE_TEST_CASE,
      'TEST_CASE',
      testCase.id,
      body.projectId,
      user.email,
      `Created test case "${body.name}"`,
      {
        testCaseName: body.name,
        category: body.category,
        priority: body.priority,
        assignedTo: body.assignedTo,
      },
      {},
      testCase.id
    );

    return ctx.json(testCase);
  }
);

ManualTestRoute.patch(
  '/test-cases/:id',
  CheckPermission([ROLES.ADMIN, ROLES.MANAGER, ROLES.STAFF]),
  CheckProjectAccess(),
  zValidator('param', ManualTestCaseSchema.schemaForIdParam),
  zValidator('json', ManualTestCaseSchema.schemaForUpdateTestCase),
  async (ctx) => {
    const { id } = ctx.req.valid('param');
    const body = ctx.req.valid('json');
    const user = ctx.get('user');

    const testCase = await db.query.ManualTestCaseTable.findFirst({
      where: (clm, { eq, and, isNull }) => and(eq(clm.id, id), isNull(clm.deletedAt)),
    });

    if (!testCase) {
      return ctx.json({ message: 'Test case not found' }, 404);
    }

    const isStaff =
      user.roles.includes(ROLES.STAFF) &&
      !user.roles.includes(ROLES.ADMIN) &&
      !user.roles.includes(ROLES.MANAGER);
    const isCreator = testCase.createdBy === user.email;
    const isAssigned = testCase.assignedToEmail === user.email;

    if (isStaff && !isCreator && !isAssigned) {
      return ctx.json(
        {
          message:
            'Forbidden: Staff members can only update test cases they created or are assigned to',
        },
        403
      );
    }

    const updatePayload: Partial<typeof ManualTestCaseTable.$inferInsert> = {
      updatedAt: dayjs().toISOString(),
      updatedBy: user.email,
    };

    const changes: any = {};

    if (body.name !== undefined && body.name !== testCase.name) {
      updatePayload.name = body.name;
      changes.name = { from: testCase.name, to: body.name };
    }
    if (body.category !== undefined && body.category !== testCase.category) {
      updatePayload.category = body.category;
      changes.category = { from: testCase.category, to: body.category };
    }
    if (body.priority !== undefined && body.priority !== testCase.priority) {
      updatePayload.priority = body.priority;
      changes.priority = { from: testCase.priority, to: body.priority };
    }
    if (body.estimatedTime !== undefined && body.estimatedTime !== testCase.estimatedTime) {
      updatePayload.estimatedTime = body.estimatedTime;
      changes.estimatedTime = { from: testCase.estimatedTime, to: body.estimatedTime };
    }
    if (body.assignedTo && body.assignedTo !== testCase.assignedToEmail) {
      updatePayload.assignedTo = body?.assignedTo?.split('@')?.[0]
        ? body?.assignedTo?.split('@')[0]
        : body.assignedTo;
      updatePayload.assignedToEmail = body.assignedTo;
      changes.assignedTo = { from: testCase.assignedToEmail, to: body.assignedTo };
    }
    if (body.status !== undefined && body.status !== testCase.status) {
      updatePayload.status = body.status;
      changes.status = { from: testCase.status, to: body.status };
    }
    if (body.dueDate !== undefined && body.dueDate !== testCase.dueDate) {
      updatePayload.dueDate = body.dueDate;
      changes.dueDate = { from: testCase.dueDate, to: body.dueDate };
    }
    if (body.dataTest !== undefined && body.dataTest !== testCase.dataTest) {
      updatePayload.dataTest = body.dataTest;
      changes.dataTest = { from: testCase.dataTest, to: body.dataTest };
    }
    if (
      body.inputDescription !== undefined &&
      body.inputDescription !== testCase.inputDescription
    ) {
      updatePayload.inputDescription = body.inputDescription;
      changes.inputDescription = { from: testCase.inputDescription, to: body.inputDescription };
    }
    if (body.desiredResults !== undefined && body.desiredResults !== testCase.desiredResults) {
      updatePayload.desiredResults = body.desiredResults;
      changes.desiredResults = { from: testCase.desiredResults, to: body.desiredResults };
    }

    const updatedTestCase = await db
      .update(ManualTestCaseTable)
      .set(updatePayload)
      .where(eq(ManualTestCaseTable.id, id))
      .returning()
      .then((res) => res[0]);

    await logActivity(
      ACTIVITY_TYPES.MANUAL_TEST_CASE_UPDATED,
      testCase.projectId,
      user.email,
      `Updated manual test case "${updatedTestCase.name}"`,
      testCase.id,
      'manual_test_case',
      {
        previousName: testCase.name,
        newName: updatedTestCase.name,
      }
    );

    await logManualTestHistory(
      ACTION_TYPES.UPDATE_TEST_CASE,
      'TEST_CASE',
      testCase.id,
      testCase.projectId,
      user.email,
      `Updated test case "${updatedTestCase.name}"`,
      changes,
      {
        testCaseName: updatedTestCase.name,
        fieldsChanged: Object.keys(changes),
      },
      testCase.id
    );

    return ctx.json(updatedTestCase);
  }
);

ManualTestRoute.delete(
  '/test-cases/:id',
  CheckPermission([ROLES.ADMIN, ROLES.MANAGER, ROLES.STAFF]),
  CheckProjectAccess(),
  zValidator('param', ManualTestCaseSchema.schemaForIdParam),
  async (ctx) => {
    const { id } = ctx.req.valid('param');
    const user = ctx.get('user');

    const testCase = await db.query.ManualTestCaseTable.findFirst({
      where: (clm, { eq, and, isNull }) => and(eq(clm.id, id), isNull(clm.deletedAt)),
    });

    if (!testCase) {
      return ctx.json({ message: 'Test case not found' }, 404);
    }

    const isStaff =
      user.roles.includes(ROLES.STAFF) &&
      !user.roles.includes(ROLES.ADMIN) &&
      !user.roles.includes(ROLES.MANAGER);
    const isCreator = testCase.createdBy === user.email;

    if (isStaff && !isCreator) {
      return ctx.json(
        {
          message: 'Forbidden: Staff members can only delete test cases they created',
        },
        403
      );
    }

    await db
      .update(ManualTestCaseTable)
      .set({
        deletedAt: dayjs().toISOString(),
        deletedBy: user.email,
      })
      .where(eq(ManualTestCaseTable.id, id));

    await logActivity(
      ACTIVITY_TYPES.MANUAL_TEST_CASE_DELETED,
      testCase.projectId,
      user.email,
      `Deleted manual test case "${testCase.name}"`,
      testCase.id,
      'manual_test_case'
    );

    await logManualTestHistory(
      ACTION_TYPES.DELETE_TEST_CASE,
      'TEST_CASE',
      testCase.id,
      testCase.projectId,
      user.email,
      `Deleted test case "${testCase.name}"`,
      {
        testCaseName: testCase.name,
        category: testCase.category,
        priority: testCase.priority,
      },
      {},
      testCase.id
    );

    return ctx.json({ message: 'Test case deleted successfully' });
  }
);

ManualTestRoute.post(
  '/test-cases/:id/execute',
  CheckPermission([ROLES.ADMIN, ROLES.MANAGER, ROLES.STAFF]),
  CheckProjectAccess(),
  zValidator('param', ManualTestCaseSchema.schemaForIdParam),
  zValidator('json', ManualTestCaseSchema.schemaForExecuteTestCase),
  async (ctx) => {
    const { id } = ctx.req.valid('param');
    const body = ctx.req.valid('json');
    const user = ctx.get('user');

    const testCase = await db.query.ManualTestCaseTable.findFirst({
      where: (clm, { eq, and, isNull }) => and(eq(clm.id, id), isNull(clm.deletedAt)),
    });

    if (!testCase) {
      return ctx.json({ message: 'Test case not found' }, 404);
    }

    const executionHistory = (testCase.executionHistory as any[]) || [];
    executionHistory.push({
      executedAt: dayjs().toISOString(),
      executedBy: user.email,
      status: body.status,
      executionTime: body.executionTime,
    });

    const updatedTestCase = await db
      .update(ManualTestCaseTable)
      .set({
        status: body.status,
        executionHistory,
        updatedAt: dayjs().toISOString(),
        updatedBy: user.email,
      })
      .where(eq(ManualTestCaseTable.id, id))
      .returning()
      .then((res) => res[0]);

    await logActivity(
      ACTIVITY_TYPES.MANUAL_TEST_CASE_EXECUTED,
      testCase.projectId,
      user.email,
      `Executed manual test case "${testCase.name}" with status: ${body.status}`,
      testCase.id,
      'manual_test_case',
      {
        status: body.status,
      }
    );

    await logManualTestHistory(
      ACTION_TYPES.EXECUTE_TEST_CASE,
      'TEST_CASE',
      testCase.id,
      testCase.projectId,
      user.email,
      `Executed test case "${testCase.name}" with result: ${body.status}`,
      {
        executionResult: body.status,
        executionTime: body.executionTime,
        previousStatus: testCase.status,
        newStatus: body.status,
      },
      {
        testCaseName: testCase.name,
        executionDuration: body.executionTime,
      },
      testCase.id
    );

    return ctx.json(updatedTestCase);
  }
);

ManualTestRoute.patch(
  '/test-cases/:id/status',
  CheckPermission([ROLES.ADMIN, ROLES.MANAGER, ROLES.STAFF]),
  CheckProjectAccess(),
  zValidator('param', ManualTestCaseSchema.schemaForIdParam),
  zValidator('json', ManualTestCaseSchema.schemaForUpdateStatus),
  async (ctx) => {
    const { id } = ctx.req.valid('param');
    const body = ctx.req.valid('json');
    const user = ctx.get('user');

    const testCase = await db.query.ManualTestCaseTable.findFirst({
      where: (clm, { eq, and, isNull }) => and(eq(clm.id, id), isNull(clm.deletedAt)),
    });

    if (!testCase) {
      return ctx.json({ message: 'Test case not found' }, 404);
    }

    const updateData: any = {
      status: body.status,
      updatedAt: dayjs().toISOString(),
      updatedBy: user.email,
    };

    const changes: any = {
      status: { from: testCase.status, to: body.status },
    };

    if (body.bugStatus) {
      updateData.bugStatusType = body.bugStatus.type;
      updateData.bugReporter = body.bugStatus.reporter || user.email;
      updateData.bugMessage = body.bugStatus.message;

      changes.bugStatus = {
        from: {
          type: testCase.bugStatusType,
          reporter: testCase.bugReporter,
          message: testCase.bugMessage,
        },
        to: body.bugStatus,
      };
    }

    const updatedTestCase = await db
      .update(ManualTestCaseTable)
      .set(updateData)
      .where(eq(ManualTestCaseTable.id, id))
      .returning()
      .then((res) => res[0]);

    await logManualTestHistory(
      ACTION_TYPES.UPDATE_TEST_CASE_STATUS,
      'TEST_CASE',
      testCase.id,
      testCase.projectId,
      user.email,
      `Updated status of test case "${testCase.name}" from ${testCase.status} to ${body.status}`,
      changes,
      {
        testCaseName: testCase.name,
        statusChange: `${testCase.status} → ${body.status}`,
      },
      testCase.id
    );

    return ctx.json(updatedTestCase);
  }
);

ManualTestRoute.post(
  '/test-cases/:testCaseId/bugs',
  CheckPermission([ROLES.ADMIN, ROLES.MANAGER, ROLES.STAFF]),
  CheckProjectAccess(),
  zValidator('param', BugSchema.schemaForTestCaseIdParam),
  zValidator(
    'json',
    BugSchema.schemaForCreateBug.omit({ priority: true }).extend({
      priority: z.enum(['High', 'Medium', 'Low']).optional(),
      attachments: z
        .array(
          z.object({
            id: z.string(),
            fileName: z.string(),
            originalName: z.string(),
            mimeType: z.string(),
            size: z.number(),
            url: z.string(),
            uploadedAt: z.string(),
          })
        )
        .optional(),
    })
  ),
  async (ctx) => {
    const { testCaseId } = ctx.req.valid('param');
    const body = ctx.req.valid('json');
    const user = ctx.get('user');

    const manualTestCase = await db.query.ManualTestCaseTable.findFirst({
      where: (clm, { eq, and, isNull }) => and(eq(clm.id, testCaseId), isNull(clm.deletedAt)),
    });

    if (!manualTestCase) {
      return ctx.json({ message: 'Manual Test Case not found' }, 404);
    }

    if (manualTestCase.projectId !== body.projectId) {
      return ctx.json({ message: 'Project ID mismatch with test case' }, 400);
    }

    const bug = await db
      .insert(BugTable)
      .values({
        id: ulid(),
        manualTestCaseId: testCaseId,
        projectId: body.projectId,
        title: body.title,
        description: body.description,
        severity: body.severity,
        priority: body.priority || 'Medium',
        status: 'Open',
        assignedToEmail: body.assignedToEmail,
        reporterEmail: user.email,
        attachments: body.attachments || [],
        createdAt: dayjs().toISOString(),
        createdBy: user.email,
      })
      .returning()
      .then((res) => res[0]);

    await logActivity(
      ACTIVITY_TYPES.BUG_CREATED,
      body.projectId,
      user.email,
      `Created bug "${bug.title}" for test case "${manualTestCase.name}"`,
      bug.id,
      'bug'
    );

    await logManualTestHistory(
      ACTION_TYPES.CREATE_BUG,
      'BUG',
      bug.id,
      body.projectId,
      user.email,
      `Created bug "${bug.title}" for test case "${manualTestCase.name}"`,
      {
        bugTitle: bug.title,
        severity: bug.severity,
        priority: bug.priority,
        assignedToEmail: bug.assignedToEmail,
        attachmentCount: (body.attachments || []).length,
      },
      {
        testCaseId: testCaseId,
        testCaseName: manualTestCase.name,
        attachments: body.attachments || [],
      },
      testCaseId
    );

    return ctx.json(bug, 201);
  }
);

ManualTestRoute.get(
  '/test-cases/:testCaseId/bugs',
  CheckPermission([ROLES.ADMIN, ROLES.MANAGER, ROLES.STAFF]),
  CheckProjectAccess(),
  zValidator('param', BugSchema.schemaForTestCaseIdParam),
  async (ctx) => {
    const { testCaseId } = ctx.req.valid('param');

    const manualTestCase = await db.query.ManualTestCaseTable.findFirst({
      where: (clm, { eq, and, isNull }) => and(eq(clm.id, testCaseId), isNull(clm.deletedAt)),
    });

    if (!manualTestCase) {
      return ctx.json({ message: 'Manual Test Case not found, cannot fetch bugs.' }, 404);
    }

    const bugs = await db.query.BugTable.findMany({
      where: (clm, { eq, and, isNull }) =>
        and(eq(clm.manualTestCaseId, testCaseId), isNull(clm.deletedAt)),
      orderBy: (clm, { desc }) => drizzleDesc(clm.createdAt),
    });
    return ctx.json(bugs);
  }
);

ManualTestRoute.patch(
  '/bugs/:id/status',
  CheckPermission([ROLES.ADMIN, ROLES.MANAGER, ROLES.STAFF]),
  zValidator('param', BugSchema.schemaForBugIdParam),
  zValidator(
    'json',
    z.object({
      status: z.enum(['Open', 'In Progress', 'Resolved', 'Closed', 'Reopened']),
    })
  ),
  async (ctx) => {
    const { id: bugId } = ctx.req.valid('param');
    const { status: newStatus } = ctx.req.valid('json');
    const user = ctx.get('user');

    const bug = await db.query.BugTable.findFirst({
      where: (clm, { eq, and, isNull }) => and(eq(clm.id, bugId), isNull(clm.deletedAt)),
      with: { project: true, manualTestCase: true },
    });

    if (!bug) {
      return ctx.json({ message: 'Bug not found' }, 404);
    }

    if (!bug.project) {
      return ctx.json({ message: 'Bug is not associated with a valid project' }, 400);
    }

    const isAdminOrManager = user.roles.includes(ROLES.ADMIN) || user.roles.includes(ROLES.MANAGER);
    if (!isAdminOrManager) {
      const assignment = await db.query.ProjectAssignmentTable.findFirst({
        where: (clm, { eq, and, isNull: dbIsNull }) =>
          and(
            eq(clm.projectId, bug.projectId),
            eq(clm.userEmail, user.email),
            dbIsNull(clm.deletedAt)
          ),
      });
      if (!assignment) {
        return ctx.json(
          { message: "Forbidden: You don't have access to this project's bugs" },
          403
        );
      }
    }

    const updatedBug = await db
      .update(BugTable)
      .set({
        status: newStatus,
        updatedAt: dayjs().toISOString(),
        updatedBy: user.email,
      })
      .where(eq(BugTable.id, bugId))
      .returning()
      .then((res) => res[0]);

    await logActivity(
      ACTIVITY_TYPES.BUG_UPDATED,
      bug.projectId,
      user.email,
      `Updated status of bug "${bug.title}" to ${newStatus}`,
      bug.id,
      'bug',
      { oldStatus: bug.status, newStatus }
    );

    await logManualTestHistory(
      ACTION_TYPES.UPDATE_BUG_STATUS,
      'BUG',
      bug.id,
      bug.projectId,
      user.email,
      `Updated bug "${bug.title}" status from ${bug.status} to ${newStatus}`,
      {
        status: { from: bug.status, to: newStatus },
      },
      {
        bugTitle: bug.title,
        testCaseId: bug.manualTestCaseId,
        testCaseName: bug.manualTestCase?.name,
        statusChange: `${bug.status} → ${newStatus}`,
      },
      bug.manualTestCaseId
    );

    return ctx.json(updatedBug);
  }
);

ManualTestRoute.patch(
  '/bugs/:id',
  CheckPermission([ROLES.ADMIN, ROLES.MANAGER, ROLES.STAFF]),
  zValidator('param', BugSchema.schemaForBugIdParam),
  zValidator(
    'json',
    z.object({
      title: z.string().min(1, 'Title is required').max(255).optional(),
      description: z.string().optional(),
      severity: z.enum(['Critical', 'High', 'Medium', 'Low']).optional(),
      priority: z.enum(['High', 'Medium', 'Low']).optional(),
      assignedToEmail: z.string().email().optional().nullable(),
      attachments: z
        .array(
          z.object({
            id: z.string(),
            fileName: z.string(),
            originalName: z.string(),
            mimeType: z.string(),
            size: z.number(),
            url: z.string(),
            uploadedAt: z.string(),
          })
        )
        .optional(),
    })
  ),
  async (ctx) => {
    const { id: bugId } = ctx.req.valid('param');
    const body = ctx.req.valid('json');
    const user = ctx.get('user');

    const bug = await db.query.BugTable.findFirst({
      where: (clm, { eq, and, isNull }) => and(eq(clm.id, bugId), isNull(clm.deletedAt)),
      with: { project: true, manualTestCase: true },
    });

    if (!bug) {
      return ctx.json({ message: 'Bug not found' }, 404);
    }

    if (!bug.project) {
      return ctx.json({ message: 'Bug is not associated with a valid project' }, 400);
    }

    const isAdminOrManager = user.roles.includes(ROLES.ADMIN) || user.roles.includes(ROLES.MANAGER);
    if (!isAdminOrManager) {
      const assignment = await db.query.ProjectAssignmentTable.findFirst({
        where: (clm, { eq, and, isNull: dbIsNull }) =>
          and(
            eq(clm.projectId, bug.projectId),
            eq(clm.userEmail, user.email),
            dbIsNull(clm.deletedAt)
          ),
      });
      if (!assignment) {
        return ctx.json(
          { message: "Forbidden: You don't have access to this project's bugs" },
          403
        );
      }
    }

    const updateData: any = {
      updatedAt: dayjs().toISOString(),
      updatedBy: user.email,
    };

    const changes: any = {};

    if (body.title !== undefined && body.title !== bug.title) {
      updateData.title = body.title;
      changes.title = { from: bug.title, to: body.title };
    }
    if (body.description !== undefined && body.description !== bug.description) {
      updateData.description = body.description;
      changes.description = { from: bug.description, to: body.description };
    }
    if (body.severity !== undefined && body.severity !== bug.severity) {
      updateData.severity = body.severity;
      changes.severity = { from: bug.severity, to: body.severity };
    }
    if (body.priority !== undefined && body.priority !== bug.priority) {
      updateData.priority = body.priority;
      changes.priority = { from: bug.priority, to: body.priority };
    }
    if (body.assignedToEmail !== undefined && body.assignedToEmail !== bug.assignedToEmail) {
      updateData.assignedToEmail = body.assignedToEmail;
      changes.assignedToEmail = { from: bug.assignedToEmail, to: body.assignedToEmail };
    }
    if (body.attachments !== undefined) {
      updateData.attachments = body.attachments;
      changes.attachments = {
        from: bug.attachments,
        to: body.attachments,
        count: {
          from: ((bug.attachments as any[]) || []).length,
          to: (body.attachments || []).length,
        },
      };
    }

    const updatedBug = await db
      .update(BugTable)
      .set(updateData)
      .where(eq(BugTable.id, bugId))
      .returning()
      .then((res) => res[0]);

    await logActivity(
      ACTIVITY_TYPES.BUG_UPDATED,
      bug.projectId,
      user.email,
      `Updated bug "${updatedBug.title}"`,
      bug.id,
      'bug',
      {
        previousTitle: bug.title,
        newTitle: updatedBug.title,
        previousSeverity: bug.severity,
        newSeverity: updatedBug.severity,
      }
    );

    await logManualTestHistory(
      ACTION_TYPES.UPDATE_BUG,
      'BUG',
      bug.id,
      bug.projectId,
      user.email,
      `Updated bug "${updatedBug.title}"`,
      changes,
      {
        bugTitle: updatedBug.title,
        testCaseId: bug.manualTestCaseId,
        testCaseName: bug.manualTestCase?.name,
        fieldsChanged: Object.keys(changes),
      },
      bug.manualTestCaseId
    );

    return ctx.json(updatedBug);
  }
);

ManualTestRoute.get(
  '/history',
  CheckPermission([ROLES.ADMIN, ROLES.MANAGER, ROLES.STAFF]),
  CheckProjectAccess(),
  zValidator(
    'query',
    z.object({
      projectId: z.string().ulid(),
      limit: z.string().optional(),
      offset: z.string().optional(),
      actionType: z.string().optional(),
      targetType: z.string().optional(),
      targetId: z.string().optional(),
      testCaseId: z.string().optional(),
      userEmail: z.string().optional(),
    })
  ),
  async (ctx) => {
    const query = ctx.req.valid('query');
    const limit = parseInt(query.limit || '50', 10);
    const offset = parseInt(query.offset || '0', 10);

    let conditions: any[] = [eq(ManualTestHistoryTable.projectId, query.projectId)];

    if (query.actionType) {
      conditions.push(eq(ManualTestHistoryTable.actionType, query.actionType as any));
    }
    if (query.targetType) {
      conditions.push(eq(ManualTestHistoryTable.targetType, query.targetType as any));
    }
    if (query.targetId) {
      conditions.push(eq(ManualTestHistoryTable.targetId, query.targetId));
    }
    if (query.testCaseId) {
      conditions.push(eq(ManualTestHistoryTable.testCaseId, query.testCaseId));
    }
    if (query.userEmail) {
      conditions.push(eq(ManualTestHistoryTable.userEmail, query.userEmail));
    }

    const historyRecords = await db.query.ManualTestHistoryTable.findMany({
      where: and(...conditions),
      orderBy: (clm, { desc }) => desc(clm.createdAt),
      limit,
      offset,
      with: {
        testCase: {
          columns: {
            name: true,
            id: true,
          },
        },
      },
    });

    const totalCount = await db
      .select({ count: count() })
      .from(ManualTestHistoryTable)
      .where(and(...conditions))
      .then((res) => res[0].count);

    return ctx.json({
      history: historyRecords,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount,
      },
    });
  }
);

function getCategoryLabel(category: string | null): string {
  if (!category) return 'Unknown';
  const categoryMap: Record<string, string> = {
    functional: 'Functional Test',
    ui: 'UI Test',
    integration: 'Integration Test',
    api: 'API Test',
    performance: 'Performance Test',
    security: 'Security Test',
  };
  return categoryMap[category] || category;
}

function getBugStatusMessage(type: string | null): string {
  if (!type) return 'Unknown';
  const messageMap: Record<string, string> = {
    none: 'No Issues',
    bug: 'Bug Found',
    fixed: 'Bug Fixed',
    testing: 'Testing',
    pending: 'Pending',
  };
  return messageMap[type] || 'Unknown';
}

export default ManualTestRoute;
