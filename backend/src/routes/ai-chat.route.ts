import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { streamGeminiChat } from '../lib/AI/GeminiAPI';
import type { ApiChatMessage } from '../lib/AI/GeminiAPI';
import VerifyToken from '@middlewars/VerifyToken';

const AIChatRoute = new Hono();

const ChatSchema = z.object({
  messages: z.array(
    z.object({
      role: z.enum(['user', 'assistant', 'system']),
      content: z.string(),
    })
  ),
});

AIChatRoute.use('/*', VerifyToken());

AIChatRoute.post('/chat', zValidator('json', ChatSchema), async (ctx) => {
  const body = ctx.req.valid('json');

  try {
    const messages: ApiChatMessage[] = body.messages;

    const hasUserMessage = messages.some((m) => m.role === 'user');
    if (!hasUserMessage) {
      return ctx.json({ error: 'No user message provided.' }, 400);
    }

    const lastUserMessage = messages.filter((m) => m.role === 'user').pop();
    let userProvidedFullContext = '';

    if (lastUserMessage) {
      userProvidedFullContext = lastUserMessage.content;
    }

    let systemPromptContent = `You are a highly specialized AI assistant for Robot Framework test automation.
Your primary goal is to help users create and modify Robot Framework test cases and test suites.
Follow these instructions VERY CAREFULLY:

1.  **Focus on the User's Explicit Request**: The user's message will contain their direct question or instruction. Address that specific request.

2.  **Contextual Code**: The user might provide existing Robot Framework code (e.g., a full test suite) as context *within their message*. This context is for your understanding ONLY.
    *   **DO NOT REPEAT OR INCLUDE ANY PART OF THE PROVIDED CONTEXT (like \`*** Settings ***\`, \`*** Variables ***\`, or existing test cases) IN YOUR RESPONSE UNLESS EXPLICITLY ASKED TO MODIFY IT.**

3.  **Response Format Rules**:
    *   **For Robot Framework CODE responses**: Always wrap in markdown code blocks with \`robotframework\` language:
        \`\`\`robotframework
        Test Case Name
            [Documentation]    Description here
            Log    Hello World
        \`\`\`
    *   **For explanations, discussions, or regular text**: Respond in plain text without code blocks.
    *   **Mixed responses**: Use appropriate formatting for each part (text + code block when needed).

4.  **Generating NEW Test Cases**:
    *   If the user asks you to "create a test case", "add a test case", or similar:
        *   **YOUR RESPONSE SHOULD START WITH THE CODE BLOCK CONTAINING THE NEW TEST CASE(S).**
        *   The code should start *directly* with the name of the first new test case.
        *   Example response:
            \`\`\`robotframework
            Simple Health Check
                [Documentation]    Verifies the basic health of the API.
                \${response}=    GET    https://api.example.com/health
                Should Be Equal As Strings    \${response.status_code}    200
                Log    Health check passed
            \`\`\`
        *   **DO NOT include \`*** Test Cases ***\` heading unless it's part of a completely new, minimal suite.**
        *   **IMPORTANT**: If you provide complete and valid Robot Framework test case code that is ready to be directly added to an existing test suite, you MUST add this exact marker on a NEW, SEPARATE LINE at the VERY END of your entire response:
            \`+++button-add-test-case\`

5.  **Other Code Requests**: 
    *   Keywords, resources, settings → wrap in appropriate code blocks
    *   Always use proper Robot Framework syntax and indentation (4 spaces)

6.  **Non-Code Requests**:
    *   Questions about Robot Framework concepts → plain text explanation
    *   General discussions → plain text
    *   Help requests → plain text with code examples if needed

7.  **Conciseness**: Be concise and focused. Provide only what is asked.

The user's latest message is:
"${userProvidedFullContext}"

Remember: Only use code blocks for actual Robot Framework code. Use plain text for explanations and discussions.`;

    const existingSystemMessageIndex = messages.findIndex((m) => m.role === 'system');
    if (existingSystemMessageIndex !== -1) {
      messages[existingSystemMessageIndex].content = systemPromptContent;
    } else {
      messages.unshift({
        role: 'system',
        content: systemPromptContent,
      });
    }

    const { readable, writable } = new TransformStream();
    const writer = writable.getWriter();
    const encoder = new TextEncoder();

    (async () => {
      try {
        for await (const chunk of streamGeminiChat(messages)) {
          await writer.write(encoder.encode(`data: ${JSON.stringify({ content: chunk })}\n\n`));
        }
        await writer.write(encoder.encode('data: [DONE]\n\n'));
      } catch (error: any) {
        console.error('Streaming error in route:', error);
        const errorMessage = error.message || 'An error occurred during streaming.';
        await writer.write(encoder.encode(`data: ${JSON.stringify({ error: errorMessage })}\n\n`));
      } finally {
        try {
          await writer.close();
        } catch (closeError) {
          console.error('Error closing writer:', closeError);
        }
      }
    })();

    return new Response(readable, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
        'Transfer-Encoding': 'chunked',
      },
    });
  } catch (error) {
    console.error('Chat error:', error);
    return ctx.json(
      {
        error: 'Failed to process chat request',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      500
    );
  }
});

AIChatRoute.post('/reset', async (ctx) => {
  return ctx.json({ message: 'Chat history reset successfully' });
});

export default AIChatRoute;
