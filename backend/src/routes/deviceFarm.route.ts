import { Hono } from 'hono';

const deviceFarmRoute = new Hono();

// AWS Device Farm Routes
deviceFarmRoute.get('/device-pools', async (c) => {
  try {
    // Import AWS Device Farm service
    const awsDeviceFarmService = require('../services/awsDeviceFarmService');

    const devicePools = await awsDeviceFarmService.getDevicePools();

    return c.json({
      success: true,
      devicePools,
      count: devicePools.length,
    });
  } catch (error) {
    console.error('Error getting device pools:', error);
    return c.json(
      {
        success: false,
        message: error.message,
      },
      500
    );
  }
});

deviceFarmRoute.get('/device-pools/:devicePoolArn/devices', async (c) => {
  try {
    const devicePoolArn = c.req.param('devicePoolArn');

    if (!devicePoolArn) {
      return c.json(
        {
          success: false,
          message: 'devicePoolArn is required',
        },
        400
      );
    }

    const awsDeviceFarmService = require('../services/awsDeviceFarmService');
    console.log(`📱 Getting devices in pool: ${devicePoolArn}`);
    const devicePool = await awsDeviceFarmService.getDevicesInPool(devicePoolArn);

    return c.json({
      success: true,
      devicePool,
    });
  } catch (error) {
    console.error('Error getting devices in pool:', error);
    return c.json(
      {
        success: false,
        message: error.message,
      },
      500
    );
  }
});

deviceFarmRoute.post('/run-robot-tests', async (c) => {
  try {
    const body = await c.req.json();
    const { testFiles, appFile, runName, devicePoolArn, appPackage, appActivity } = body;

    if (!testFiles || !testFiles.length) {
      return c.json(
        {
          success: false,
          message: 'testFiles are required',
        },
        400
      );
    }

    if (!appFile) {
      return c.json(
        {
          success: false,
          message: 'appFile is required',
        },
        400
      );
    }

    console.log(`🚀 Starting Robot Framework test run: ${runName}`);

    const awsDeviceFarmService = require('../services/awsDeviceFarmService');
    const fs = require('fs-extra');
    const path = require('path');

    // Create temporary directory for uploads
    const tempDir = path.join(__dirname, '../../temp', Date.now().toString());
    await fs.ensureDir(tempDir);

    try {
      // 1. Create test package
      const testPackagePath = await awsDeviceFarmService.createRobotTestPackage(testFiles, tempDir);

      // 2. Upload test package
      const testPackageArn = await awsDeviceFarmService.uploadFile(
        testPackagePath,
        'APPIUM_PYTHON_TEST_PACKAGE',
        `${runName}-tests.zip`
      );

      // 3. Upload app (if provided as file path)
      let appArn;
      if (appFile.path) {
        appArn = await awsDeviceFarmService.uploadFile(
          appFile.path,
          appFile.platform === 'ios' ? 'IOS_APP' : 'ANDROID_APP',
          appFile.name
        );
      } else {
        appArn = appFile.arn; // Use existing app ARN
      }

      // 4. Upload test spec
      const testSpecContent = awsDeviceFarmService.generateRobotTestSpec();
      const testSpecPath = path.join(tempDir, 'testspec.yml');
      await fs.writeFile(testSpecPath, testSpecContent);

      const testSpecArn = await awsDeviceFarmService.uploadFile(
        testSpecPath,
        'APPIUM_PYTHON_TEST_SPEC',
        `${runName}-testspec.yml`
      );

      // 5. Schedule test run
      const runArn = await awsDeviceFarmService.scheduleRun(
        appArn,
        testPackageArn,
        runName,
        devicePoolArn,
        testSpecArn
      );

      // Return run information
      return c.json({
        success: true,
        runArn,
        message: 'Test run scheduled successfully',
        testPackageArn,
        appArn,
        testSpecArn,
      });
    } finally {
      // Cleanup temp directory
      await fs.remove(tempDir);
    }
  } catch (error) {
    console.error('Error running Robot tests:', error);
    return c.json(
      {
        success: false,
        message: error.message,
      },
      500
    );
  }
});

deviceFarmRoute.get('/runs/:runArn/status', async (c) => {
  try {
    const runArn = c.req.param('runArn');

    if (!runArn) {
      return c.json(
        {
          success: false,
          message: 'runArn is required',
        },
        400
      );
    }

    console.log(`📊 Getting run status: ${runArn}`);

    const awsDeviceFarmService = require('../services/awsDeviceFarmService');
    const result = await awsDeviceFarmService.monitorRun(runArn, null);

    return c.json({
      success: true,
      ...result,
    });
  } catch (error) {
    console.error('Error getting run status:', error);
    return c.json(
      {
        success: false,
        message: error.message,
      },
      500
    );
  }
});

deviceFarmRoute.get('/runs/:runArn/results', async (c) => {
  try {
    const runArn = c.req.param('runArn');

    if (!runArn) {
      return c.json(
        {
          success: false,
          message: 'runArn is required',
        },
        400
      );
    }

    console.log(`📊 Getting test results: ${runArn}`);

    const awsDeviceFarmService = require('../services/awsDeviceFarmService');
    const results = await awsDeviceFarmService.getTestResults(runArn);

    return c.json({
      success: true,
      results,
    });
  } catch (error) {
    console.error('Error getting test results:', error);
    return c.json(
      {
        success: false,
        message: error.message,
      },
      500
    );
  }
});

deviceFarmRoute.get('/test-template', async (c) => {
  try {
    const platform = c.req.query('platform') || 'android';

    const template = `*** Settings ***
Documentation    Mobile App Test using Robot Framework and Appium
Library          AppiumLibrary
Suite Setup      Open Application
Suite Teardown   Close Application

*** Variables ***
\${PLATFORM_NAME}      \${PLATFORM_NAME}
\${AUTOMATION_NAME}    \${AUTOMATION_NAME}
\${DEVICE_NAME}        \${DEVICE_NAME}
\${PLATFORM_VERSION}   \${PLATFORM_VERSION}
\${APPIUM_URL}         \${APPIUM_URL}
\${APP_PACKAGE}        \${APP_PACKAGE}
\${APP_ACTIVITY}       \${APP_ACTIVITY}

*** Test Cases ***
Launch App Test
    [Documentation]    Test app launch and basic functionality
    [Tags]    smoke

    # Wait for app to load
    Wait Until Page Contains Element    accessibility_id=main_screen    timeout=30s

    # Take screenshot
    Capture Page Screenshot    app_launched.png

    # Basic interaction test
    Click Element    accessibility_id=button_example
    Wait Until Page Contains Element    accessibility_id=result_text    timeout=10s

    # Verify result
    Element Should Contain Text    accessibility_id=result_text    Expected Text

*** Keywords ***
Open Application
    [Documentation]    Open the mobile application
    Open Application    \${APPIUM_URL}
    ...    platformName=\${PLATFORM_NAME}
    ...    automationName=\${AUTOMATION_NAME}
    ...    deviceName=\${DEVICE_NAME}
    ...    platformVersion=\${PLATFORM_VERSION}
    ...    appPackage=\${APP_PACKAGE}
    ...    appActivity=\${APP_ACTIVITY}
    ...    newCommandTimeout=300

Close Application
    [Documentation]    Close the mobile application
    Close Application`;

    return c.json({
      success: true,
      template,
      platform,
      description: 'Robot Framework test template for AWS Device Farm',
    });
  } catch (error) {
    console.error('Error getting test template:', error);
    return c.json(
      {
        success: false,
        message: error.message,
      },
      500
    );
  }
});

export default deviceFarmRoute;
