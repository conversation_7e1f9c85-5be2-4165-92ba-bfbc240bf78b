import { Hono } from 'hono';
import awsDeviceFarmService from '../services/awsDeviceFarmService';
import fs from 'fs-extra';
import path from 'path';

const deviceFarmRoute = new Hono();

// AWS Device Farm Routes

// Get list of projects
deviceFarmRoute.get('/projects', async (c) => {
  try {
    const projects = await awsDeviceFarmService.getProjects();

    return c.json({
      success: true,
      projects,
      count: projects.length,
    });
  } catch (error: any) {
    console.error('❌ Error getting projects:', error);
    return c.json(
      {
        success: false,
        message: error.message,
      },
      500
    );
  }
});

// Get test suites for a project
deviceFarmRoute.get('/projects/:projectId/test-suites', async (c) => {
  try {
    const projectId = c.req.param('projectId');

    if (!projectId) {
      return c.json(
        {
          success: false,
          message: 'Project ID is required',
        },
        400
      );
    }

    const testSuites = await awsDeviceFarmService.getTestSuites(projectId);

    return c.json({
      success: true,
      testSuites,
      count: testSuites.length,
    });
  } catch (error: any) {
    console.error('❌ Error getting test suites:', error);
    return c.json(
      {
        success: false,
        message: error.message,
      },
      500
    );
  }
});

// Get list of uploaded apps
deviceFarmRoute.get('/apps', async (c) => {
  try {
    const apps = await awsDeviceFarmService.getUploadedApps();

    return c.json({
      success: true,
      apps,
      count: apps.length,
    });
  } catch (error: any) {
    console.error('❌ Error getting uploaded apps:', error);
    return c.json(
      {
        success: false,
        message: error.message,
      },
      500
    );
  }
});

// Upload new app file
deviceFarmRoute.post('/apps/upload', async (c) => {
  try {
    const body = await c.req.json();
    const { filePath, platform, name } = body;

    if (!filePath) {
      return c.json(
        {
          success: false,
          message: 'File path is required',
        },
        400
      );
    }

    if (!platform || !['android', 'ios'].includes(platform)) {
      return c.json(
        {
          success: false,
          message: 'Platform is required and must be either "android" or "ios"',
        },
        400
      );
    }

    if (!name) {
      return c.json(
        {
          success: false,
          message: 'App name is required',
        },
        400
      );
    }

    console.log(`📱 Uploading ${platform} app: ${name}`);

    const appArn = await awsDeviceFarmService.uploadFile(
      filePath,
      platform === 'ios' ? 'IOS_APP' : 'ANDROID_APP',
      name
    );

    console.log(`✅ App uploaded successfully: ${appArn}`);

    return c.json({
      success: true,
      appArn,
      message: 'App uploaded successfully',
      app: {
        arn: appArn,
        name,
        platform,
        uploadedAt: new Date().toISOString(),
      },
    });
  } catch (error: any) {
    console.error('❌ Error uploading app:', error);
    return c.json(
      {
        success: false,
        message: error.message,
      },
      500
    );
  }
});

// Get app details by ARN
deviceFarmRoute.get('/apps/:appArn', async (c) => {
  try {
    const appArn = c.req.param('appArn');

    if (!appArn) {
      return c.json(
        {
          success: false,
          message: 'App ARN is required',
        },
        400
      );
    }

    const appDetails = await awsDeviceFarmService.getAppDetails(appArn);

    return c.json({
      success: true,
      app: appDetails,
    });
  } catch (error: any) {
    console.error('❌ Error getting app details:', error);
    return c.json(
      {
        success: false,
        message: error.message,
      },
      500
    );
  }
});

deviceFarmRoute.get('/device-pools', async (c) => {
  try {
    const devicePools = await awsDeviceFarmService.getDevicePools();

    return c.json({
      success: true,
      devicePools,
      count: devicePools.length,
    });
  } catch (error) {
    console.error('Error getting device pools:', error);
    return c.json(
      {
        success: false,
        message: error.message,
      },
      500
    );
  }
});

deviceFarmRoute.get('/device-pools/:devicePoolArn/devices', async (c) => {
  try {
    const devicePoolArn = c.req.param('devicePoolArn');

    if (!devicePoolArn) {
      return c.json(
        {
          success: false,
          message: 'devicePoolArn is required',
        },
        400
      );
    }

    const devicePool = await awsDeviceFarmService.getDevicesInPool(devicePoolArn);

    return c.json({
      success: true,
      devicePool,
    });
  } catch (error) {
    console.error('Error getting devices in pool:', error);
    return c.json(
      {
        success: false,
        message: error.message,
      },
      500
    );
  }
});

deviceFarmRoute.post('/run-robot-tests', async (c) => {
  try {
    const body = await c.req.json();
    const { testFiles, appFile, runName, devicePoolArn } = body;

    if (!testFiles || !testFiles.length) {
      return c.json(
        {
          success: false,
          message: 'testFiles are required',
        },
        400
      );
    }

    if (!appFile) {
      return c.json(
        {
          success: false,
          message: 'appFile is required',
        },
        400
      );
    }

    // Create temporary directory for uploads
    const tempDir = path.join(__dirname, '../../temp', Date.now().toString());
    await fs.ensureDir(tempDir);

    try {
      // 1. Create test package
      const testPackagePath = (await awsDeviceFarmService.createRobotTestPackage(
        testFiles,
        tempDir
      )) as string;

      const testPackageArn = await awsDeviceFarmService.uploadFile(
        testPackagePath,
        'APPIUM_PYTHON_TEST_PACKAGE',
        `${runName}-tests.zip`
      );

      // 3. Upload app (if provided as file path)
      let appArn;
      if (appFile.path) {
        appArn = await awsDeviceFarmService.uploadFile(
          appFile.path,
          appFile.platform === 'ios' ? 'IOS_APP' : 'ANDROID_APP',
          appFile.name
        );
      } else if (appFile.arn) {
        appArn = appFile.arn;
      } else {
        throw new Error(
          'App file path or ARN is required. Please provide either:\n' +
            '1. appFile.path - Path to app file for upload\n' +
            '2. appFile.arn - ARN of existing uploaded app\n' +
            'Example: { "appFile": { "name": "app.apk", "platform": "android", "path": "/path/to/app.apk" } }'
        );
      }

      // Validate appArn format
      if (!appArn || !appArn.startsWith('arn:aws:devicefarm:')) {
        throw new Error(
          `Invalid app ARN format: ${appArn}. Expected format: arn:aws:devicefarm:region:account:app:project-id/app-id`
        );
      }

      // 4. Upload test spec
      const testSpecContent = awsDeviceFarmService.generatePythonRobotTestSpec();
      const testSpecPath = path.join(tempDir, 'testspec.yml');
      await fs.writeFile(testSpecPath, testSpecContent);

      const testSpecArn = await awsDeviceFarmService.uploadFile(
        testSpecPath,
        'APPIUM_PYTHON_TEST_SPEC',
        `${runName}-testspec.yml`
      );
      console.log(`✅ Test spec uploaded successfully: ${testSpecArn}`);

      // Validate all ARNs before scheduling run
      console.log('🔍 Validating ARNs before scheduling run...');
      console.log(`App ARN: ${appArn}`);
      console.log(`Test Package ARN: ${testPackageArn}`);
      console.log(`Device Pool ARN: ${devicePoolArn}`);
      console.log(`Test Spec ARN: ${testSpecArn}`);

      // Validate devicePoolArn format
      if (!devicePoolArn || !devicePoolArn.startsWith('arn:aws:devicefarm:')) {
        throw new Error(`Invalid device pool ARN format: ${devicePoolArn}`);
      }

      // Validate testPackageArn format
      if (!testPackageArn || !testPackageArn.startsWith('arn:aws:devicefarm:')) {
        throw new Error(`Invalid test package ARN format: ${testPackageArn}`);
      }

      // Validate testSpecArn format
      if (!testSpecArn || !testSpecArn.startsWith('arn:aws:devicefarm:')) {
        throw new Error(`Invalid test spec ARN format: ${testSpecArn}`);
      }

      // 5. Schedule test run
      console.log(`🚀 Scheduling test run: ${runName}...`);
      const runArn = await awsDeviceFarmService.scheduleRun(
        appArn,
        testPackageArn,
        runName,
        devicePoolArn,
        testSpecArn
      );

      // Return run information
      return c.json({
        success: true,
        runArn,
        message: 'Test run scheduled successfully',
        testPackageArn,
        appArn,
        testSpecArn,
      });
    } finally {
      // Cleanup temp directory
      await fs.remove(tempDir);
    }
  } catch (error) {
    console.error('Error running Robot tests:', error);
    return c.json(
      {
        success: false,
        message: error.message,
      },
      500
    );
  }
});

deviceFarmRoute.get('/runs/:runArn/status', async (c) => {
  try {
    const runArn = c.req.param('runArn');

    if (!runArn) {
      return c.json(
        {
          success: false,
          message: 'runArn is required',
        },
        400
      );
    }

    const result = await awsDeviceFarmService.monitorRun(runArn, (progress) => {});

    return c.json({
      success: true,
      ...result,
    });
  } catch (error) {
    console.error('Error getting run status:', error);
    return c.json(
      {
        success: false,
        message: error.message,
      },
      500
    );
  }
});

deviceFarmRoute.get('/runs/:runArn/results', async (c) => {
  try {
    const runArn = c.req.param('runArn');

    if (!runArn) {
      return c.json(
        {
          success: false,
          message: 'runArn is required',
        },
        400
      );
    }

    const results = await awsDeviceFarmService.getTestResults(runArn);

    return c.json({
      success: true,
      results,
    });
  } catch (error) {
    console.error('Error getting test results:', error);
    return c.json(
      {
        success: false,
        message: error.message,
      },
      500
    );
  }
});

deviceFarmRoute.get('/test-template', async (c) => {
  try {
    const platform = c.req.query('platform') || 'android';

    const template = `*** Settings ***
Documentation    Mobile App Test using Robot Framework and Appium
Library          AppiumLibrary
Suite Setup      Open Application
Suite Teardown   Close Application

*** Variables ***
\${PLATFORM_NAME}      \${PLATFORM_NAME}
\${AUTOMATION_NAME}    \${AUTOMATION_NAME}
\${DEVICE_NAME}        \${DEVICE_NAME}
\${PLATFORM_VERSION}   \${PLATFORM_VERSION}
\${APPIUM_URL}         \${APPIUM_URL}
\${APP_PACKAGE}        \${APP_PACKAGE}
\${APP_ACTIVITY}       \${APP_ACTIVITY}

*** Test Cases ***
Launch App Test
    [Documentation]    Test app launch and basic functionality
    [Tags]    smoke

    # Wait for app to load
    Wait Until Page Contains Element    accessibility_id=main_screen    timeout=30s

    # Take screenshot
    Capture Page Screenshot    app_launched.png

    # Basic interaction test
    Click Element    accessibility_id=button_example
    Wait Until Page Contains Element    accessibility_id=result_text    timeout=10s

    # Verify result
    Element Should Contain Text    accessibility_id=result_text    Expected Text

*** Keywords ***
Open Application
    [Documentation]    Open the mobile application
    Open Application    \${APPIUM_URL}
    ...    platformName=\${PLATFORM_NAME}
    ...    automationName=\${AUTOMATION_NAME}
    ...    deviceName=\${DEVICE_NAME}
    ...    platformVersion=\${PLATFORM_VERSION}
    ...    appPackage=\${APP_PACKAGE}
    ...    appActivity=\${APP_ACTIVITY}
    ...    newCommandTimeout=300

Close Application
    [Documentation]    Close the mobile application
    Close Application`;

    return c.json({
      success: true,
      template,
      platform,
      description: 'Robot Framework test template for AWS Device Farm',
    });
  } catch (error) {
    console.error('Error getting test template:', error);
    return c.json(
      {
        success: false,
        message: error.message,
      },
      500
    );
  }
});

export default deviceFarmRoute;
