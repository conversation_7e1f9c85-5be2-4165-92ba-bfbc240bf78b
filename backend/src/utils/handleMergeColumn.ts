import { cloneDeep } from 'lodash';
import xlsx from 'xlsx';

/**
 * Hàm dùng để điền các giá trị cho các ô đã merge lại với nhau.
 * @param aoaData
 * @param merges
 */
export const mergeColumnByAoaData = (aoaData: any[], merges: xlsx.Range[]) => {
  merges.forEach((merge) => {
    const { s, e } = merge;
    const mergeValue = aoaData[s.r][s.c];

    for (let r = s.r; r <= e.r; r++) {
      for (let c = s.c; c <= e.c; c++) {
        if (r !== s.r || c !== s.c) {
          if (!aoaData[r]) aoaData[r] = [];
          aoaData[r][c] = mergeValue;
        }
      }
    }
  });
};
