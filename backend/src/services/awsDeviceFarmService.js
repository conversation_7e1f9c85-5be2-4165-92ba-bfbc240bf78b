const AWS = require('aws-sdk');
const fs = require('fs-extra');
const path = require('path');
const archiver = require('archiver');
const axios = require('axios');

class AWSDeviceFarmService {
  constructor() {
    this.deviceFarm = new AWS.DeviceFarm({
      region: 'us-west-2',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    });
    
    this.projectArn = process.env.AWS_DEVICE_FARM_PROJECT_ARN;
  }

  generateRobotTestSpec() {
    return `version: 0.1

    # This flag enables your test to run using Device Farm's Amazon Linux 2 test host. For more information,
    # please see https://docs.aws.amazon.com/devicefarm/latest/developerguide/amazon-linux-2.html
    android_test_host: amazon_linux_2

    # Phases represent collections of commands that are executed during your test run on the test host.
    phases:

      # The install phase contains commands for installing dependencies to run your tests.
      # For your convenience, certain dependencies are preinstalled on the test host. To lean about which
      # software is included with the host, and how to install additional software, please see:
      # https://docs.aws.amazon.com/devicefarm/latest/developerguide/amazon-linux-2-supported-software.html

      # Many software libraries you may need are available from the test host using the devicefarm-cli tool.
      # To learn more about what software is available from it and how to use it, please see:
      # https://docs.aws.amazon.com/devicefarm/latest/developerguide/amazon-linux-2-devicefarm-cli.html
      install:
        commands:
          # The Appium server is written using Node.js. In order to run your desired version of Appium,
          # you first need to set up a Node.js environment that is compatible with your version of Appium.
          - devicefarm-cli use node 18
          - node --version

          # Use the devicefarm-cli to select a preinstalled major version of Appium.
          - devicefarm-cli use appium 2
          - appium --version

          # The Device Farm service automatically updates the preinstalled Appium versions over time to
          # incorporate the latest minor and patch versions for each major version. If you wish to
          # select a specific version of Appium, you can use NPM to install it.
          # - npm install -g appium@2.1.3

          # For Appium version 2, Device Farm automatically updates the preinstalled UIAutomator2 driver
          # over time to incorporate the latest minor and patch versions for its major version 2. If you
          # want to install a specific version of the driver, you can use the Appium extension CLI to
          # uninstall the existing UIAutomator2 driver and install your desired version:
          # - appium driver uninstall uiautomator2
          # - appium driver install uiautomator2@2.34.0

          # We recommend setting the Appium server's base path explicitly for accepting commands.
          - export APPIUM_BASE_PATH=/wd/hub

          # Install the NodeJS dependencies.
          - cd $DEVICEFARM_TEST_PACKAGE_PATH
          # First, install dependencies which were packaged with the test package using npm-bundle.
          - npm install *.tgz
          # Then, optionally, install any additional dependencies using npm install.
          # If you do run these commands, we strongly recommend that you include your package-lock.json
          # file with your test package so that the dependencies installed on Device Farm match
          # the dependencies you've installed locally.
          # - cd node_modules/*
          # - npm install

      # The pre-test phase contains commands for setting up your test environment.
      pre_test:
        commands:
          # Appium downloads Chromedriver using a feature that is considered insecure for multitenant
          # environments. This is not a problem for Device Farm because each test host is allocated
          # exclusively for one customer, then terminated entirely. For more information, please see
          # https://github.com/appium/appium/blob/master/packages/appium/docs/en/guides/security.md

          # We recommend starting the Appium server process in the background using the command below.
          # The Appium server log will be written to the $DEVICEFARM_LOG_DIR directory.
          # The environment variables passed as capabilities to the server will be automatically assigned
          # during your test run based on your test's specific device.
          # For more information about which environment variables are set and how they're set, please see
          # https://docs.aws.amazon.com/devicefarm/latest/developerguide/custom-test-environment-variables.html
          - |-
            appium --base-path=$APPIUM_BASE_PATH --log-timestamp \
              --log-no-colors --relaxed-security --default-capabilities \
              "{\"appium:deviceName\": \"$DEVICEFARM_DEVICE_NAME\", \
              \"platformName\": \"$DEVICEFARM_DEVICE_PLATFORM_NAME\", \
              \"appium:app\": \"$DEVICEFARM_APP_PATH\", \
              \"appium:udid\":\"$DEVICEFARM_DEVICE_UDID\", \
              \"appium:platformVersion\": \"$DEVICEFARM_DEVICE_OS_VERSION\", \
              \"appium:chromedriverExecutableDir\": \"$DEVICEFARM_CHROMEDRIVER_EXECUTABLE_DIR\", \
              \"appium:automationName\": \"UiAutomator2\"}" \
              >> $DEVICEFARM_LOG_DIR/appium.log 2>&1 &

          # This code will wait until the Appium server starts.
          - |-
            appium_initialization_time=0;
            until curl --silent --fail "http://0.0.0.0:4723/wd/hub/status"; do
              if [[ $appium_initialization_time -gt 30 ]]; then
                echo "Appium did not start within 30 seconds. Exiting...";
                exit 1;
              fi;
              appium_initialization_time=$((appium_initialization_time + 1));
              echo "Waiting for Appium to start on port 4723...";
              sleep 1;
            done;

      # The test phase contains commands for running your tests.
      test:
        commands:
          # Your test package is downloaded and unpackaged into the $DEVICEFARM_TEST_PACKAGE_PATH directory.
          # When compiling with npm-bundle, the test folder can be found in the node_modules/*/ subdirectory.
          - cd $DEVICEFARM_TEST_PACKAGE_PATH/node_modules/*
          - echo "Starting the Appium NodeJS test"
          
          # Enter your command below to start the tests. The command should be the same command as the one 
          # you use to run your tests locally from the command line. An example, "npm test", is given below:
          - npm test

      # The post-test phase contains commands that are run after your tests have completed.
      # If you need to run any commands to generating logs and reports on how your test performed,
      # we recommend adding them to this section.
      post_test:
        commands:

    # Artifacts are a list of paths on the filesystem where you can store test output and reports.
    # All files in these paths will be collected by Device Farm.
    # These files will be available through the ListArtifacts API as your "Customer Artifacts".
    artifacts:
      # By default, Device Farm will collect your artifacts from the $DEVICEFARM_LOG_DIR directory.
      - $DEVICEFARM_LOG_DIR`;
  } 

  /**
   * Create test package from Robot Framework files
   */
  async createRobotTestPackage(testFiles, outputPath) {
    try {
      const packagePath = path.join(outputPath, 'robot-tests.zip');
      const output = fs.createWriteStream(packagePath);
      const archive = archiver('zip', { zlib: { level: 9 } });

      return new Promise((resolve, reject) => {
        output.on('close', () => {
          console.log(`✅ Test package created: ${archive.pointer()} bytes`);
          resolve(packagePath);
        });

        archive.on('error', reject);
        archive.pipe(output);

        // Add Robot Framework files
        testFiles.forEach(file => {
          if (file.content) {
            archive.append(file.content, { name: file.name });
          } else if (file.path) {
            archive.file(file.path, { name: file.name });
          }
        });

        // Add requirements.txt for Robot Framework
        const requirements = `robotframework==6.1.1
          robotframework-appiumlibrary==2.0.0
          appium-python-client==3.1.0
          selenium==4.15.0`;
        
        archive.append(requirements, { name: 'requirements.txt' });

        // Add test spec YAML
        const testSpec = this.generateRobotTestSpec();
        archive.append(testSpec, { name: 'testspec.yml' });

        archive.finalize();
      });
    } catch (error) {
      console.error('❌ Error creating test package:', error);
      throw error;
    }
  }

  /**
   * Upload file to Device Farm
   */
  async uploadFile(filePath, type, name) {
    try {
    
      // Create upload
      const createUploadParams = {
        projectArn: this.projectArn,
        name: name,
        type: type,
        contentType: 'application/octet-stream'
      };

      const uploadResponse = await this.deviceFarm.createUpload(createUploadParams).promise();
      const uploadArn = uploadResponse.upload.arn;
      const uploadUrl = uploadResponse.upload.url;

      // Upload file to S3
      const fileContent = await fs.readFile(filePath);
      await axios.put(uploadUrl, fileContent, {
        headers: {
          'Content-Type': 'application/octet-stream'
        }
      });

      // Wait for upload to be processed
      await this.waitForUploadProcessing(uploadArn);
      return uploadArn;
    } catch (error) {
      console.error(`Error uploading ${type}:`, error);
      throw error;
    }
  }

  /**
   * Wait for upload processing
   */
  async waitForUploadProcessing(uploadArn, maxWaitTime = 300000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      const upload = await this.deviceFarm.getUpload({ arn: uploadArn }).promise();
      const status = upload.upload.status;
      
      if (status === 'SUCCEEDED') {
        return true;
      } else if (status === 'FAILED') {
        throw new Error(`Upload failed: ${upload?.upload?.message || JSON.stringify(upload)}`);
      }
      
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
    
    throw new Error('Upload processing timeout');
  }

  /**
   * Schedule test run on Device Farm
   */
  async scheduleRun(appArn, testPackageArn, runName, devicePoolArn, testSpecArn) {
    try {
      const runParams = {
        projectArn: this.projectArn,
        appArn: appArn,
        devicePoolArn: devicePoolArn || this.defaultDevicePools.android,
        name: runName,
        test: {
          type: 'APPIUM_PYTHON',
          testPackageArn: testPackageArn,
          testSpecArn: testSpecArn
        },
        configuration: {
          billingMethod: 'METERED',
          locale: 'en_US',
          location: {
            latitude: 47.6204,
            longitude: -122.3491
          }
        }
      };

      const runResponse = await this.deviceFarm.scheduleRun(runParams).promise();
      const runArn = runResponse.run.arn;

      return runArn;
    } catch (error) {
      console.error('Error scheduling run:', error);
      throw error;
    }
  }

  /**
   * Monitor test run progress
   */
  async monitorRun(runArn, onProgress) {
    try {
      let isCompleted = false;
      const startTime = Date.now();
      
      while (!isCompleted) {
        const run = await this.deviceFarm.getRun({ arn: runArn }).promise();
        const status = run.run.status;
        const result = run.run.result;
        
        const progress = {
          status,
          result,
          message: run.run.message,
          totalJobs: run.run.totalJobs,
          completedJobs: run.run.completedJobs,
          elapsedTime: Date.now() - startTime
        };
        
        if (onProgress) {
          onProgress(progress);
        }
        
        if (status === 'COMPLETED' || status === 'ERRORED') {
          isCompleted = true;
          return {
            status,
            result,
            run: run.run
          };
        }
        
        await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
      }
    } catch (error) {
      console.error('Error monitoring run:', error);
      throw error;
    }
  }

  /**
   * Get test results and artifacts
   */
  async getTestResults(runArn) {
    try {
      // Get jobs
      const jobs = await this.deviceFarm.listJobs({ arn: runArn }).promise();
      const results = [];
      
      for (const job of jobs.jobs) {
        const jobResult = {
          device: job.device,
          status: job.status,
          result: job.result,
          message: job.message,
          suites: []
        };
        
        // Get suites
        const suites = await this.deviceFarm.listSuites({ arn: job.arn }).promise();
        
        for (const suite of suites.suites) {
          const suiteResult = {
            name: suite.name,
            status: suite.status,
            result: suite.result,
            tests: []
          };
          
          // Get tests
          const tests = await this.deviceFarm.listTests({ arn: suite.arn }).promise();
          
          for (const test of tests.tests) {
            const testResult = {
              name: test.name,
              status: test.status,
              result: test.result,
              message: test.message,
              artifacts: await this.getArtifacts(test.arn)
            };
            
            suiteResult.tests.push(testResult);
          }
          
          jobResult.suites.push(suiteResult);
        }
        
        results.push(jobResult);
      }
      
      return results;
    } catch (error) {
      console.error('Error getting test results:', error);
      throw error;
    }
  }

  /**
   * Get artifacts for a test
   */
  async getArtifacts(testArn) {
    try {
      const artifacts = {};
      const artifactTypes = ['FILE', 'LOG', 'SCREENSHOT'];
      
      for (const type of artifactTypes) {
        const artifactList = await this.deviceFarm.listArtifacts({
          arn: testArn,
          type: type
        }).promise();
        
        artifacts[type.toLowerCase()] = artifactList.artifacts;
      }
      
      return artifacts;
    } catch (error) {
      console.error('Error getting artifacts:', error);
      return {};
    }
  }

  /**
   * Get available device pools
   */
  async getDevicePools() {
    try {
      const devicePools = await this.deviceFarm.listDevicePools({
        arn: this.projectArn
      }).promise();
      
      return devicePools.devicePools;
    } catch (error) {
      console.error('Error getting device pools:', error);
      throw error;
    }
  }

  /**
   * Get devices in a pool
   */
  async getDevicesInPool(devicePoolArn) {
    try {
      const devices = await this.deviceFarm.getDevicePool({
        arn: devicePoolArn
      }).promise();
      
      return devices.devicePool;
    } catch (error) {
      console.error('Error getting devices in pool:', error);
      throw error;
    }
  }
}

module.exports = new AWSDeviceFarmService();
