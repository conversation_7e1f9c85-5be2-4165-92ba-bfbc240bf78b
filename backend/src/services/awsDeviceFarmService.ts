import AWS from 'aws-sdk';
import fs from 'fs-extra';
import path from 'path';
import archiver from 'archiver';
import axios from 'axios';

interface TestFile {
  name: string;
  content?: string;
  path?: string;
}

class AWSDeviceFarmService {
  private deviceFarm: AWS.DeviceFarm;
  private projectArn: string;

  constructor() {
    this.deviceFarm = new AWS.DeviceFarm({
      region: 'us-west-2', // Device Farm only available in us-west-2
    });

    this.projectArn = process.env.AWS_DEVICE_FARM_PROJECT_ARN || '';
  }

  /**
   * Generate test spec YAML for Robot Framework
   */
  generateRobotTestSpec(): string {
    return `version: 0.1

      android_test_host: amazon_linux_2

      phases:
        install:
          commands:
            # Install Python and Robot Framework dependencies
            - echo "Installing Robot Framework and dependencies..."
            - pip3 install --upgrade pip
            - pip3 install -r requirements.txt
            
            # Setup Node.js and Appium
            - devicefarm-cli use node 18
            - devicefarm-cli use appium 2
            - appium driver install uiautomator2
            - appium driver install xcuitest
            
            # Set Appium base path
            - export APPIUM_BASE_PATH=/wd/hub

        pre_test:
          commands:
            - echo "Starting Appium server..."
            - |-
              appium --base-path=$APPIUM_BASE_PATH --log-timestamp \\
                --log-no-colors --relaxed-security --default-capabilities \\
                "{\\"appium:deviceName\\": \\"$DEVICEFARM_DEVICE_NAME\\", \\
                \\"platformName\\": \\"$DEVICEFARM_DEVICE_PLATFORM_NAME\\", \\
                \\"appium:app\\": \\"$DEVICEFARM_APP_PATH\\", \\
                \\"appium:udid\\":\\"$DEVICEFARM_DEVICE_UDID\\", \\
                \\"appium:platformVersion\\": \\"$DEVICEFARM_DEVICE_OS_VERSION\\", \\
                \\"appium:chromedriverExecutableDir\\": \\"$DEVICEFARM_CHROMEDRIVER_EXECUTABLE_DIR\\", \\
                \\"appium:automationName\\": \\"UiAutomator2\\"}" \\
                >> $DEVICEFARM_LOG_DIR/appium.log 2>&1 &
            
            # Wait for Appium to start
            - |-
              appium_initialization_time=0;
              until curl --silent --fail "http://0.0.0.0:4723/wd/hub/status"; do
                if [[ $appium_initialization_time -gt 30 ]]; then
                  echo "Appium did not start within 30 seconds. Exiting...";
                  exit 1;
                fi;
                appium_initialization_time=$((appium_initialization_time + 1));
                echo "Waiting for Appium to start on port 4723...";
                sleep 1;
              done;

        test:
          commands:
            - echo "Running Robot Framework tests..."
            - cd $DEVICEFARM_TEST_PACKAGE_PATH
            - |
              # Set device capabilities based on platform
              if [ "$DEVICEFARM_DEVICE_PLATFORM_NAME" = "Android" ]; then
                export PLATFORM_NAME=Android
                export AUTOMATION_NAME=UIAutomator2
              else
                export PLATFORM_NAME=iOS
                export AUTOMATION_NAME=XCUITest
              fi
              
              export DEVICE_NAME=$DEVICEFARM_DEVICE_NAME
              export PLATFORM_VERSION=$DEVICEFARM_DEVICE_OS_VERSION
              export APPIUM_URL=http://localhost:4723/wd/hub
              
              # Run Robot Framework tests
              robot --outputdir $DEVICEFARM_LOG_DIR \\
                    --variable PLATFORM_NAME:$PLATFORM_NAME \\
                    --variable AUTOMATION_NAME:$AUTOMATION_NAME \\
                    --variable DEVICE_NAME:$DEVICE_NAME \\
                    --variable PLATFORM_VERSION:$PLATFORM_VERSION \\
                    --variable APPIUM_URL:$APPIUM_URL \\
                    --variable APP_PACKAGE:$APP_PACKAGE \\
                    --variable APP_ACTIVITY:$APP_ACTIVITY \\
                    tests/

        post_test:
          commands:
            - echo "Test execution completed"
            - echo "Collecting artifacts..."

      artifacts:
        - $DEVICEFARM_LOG_DIR`;
  }

  /**
   * Create test package from Robot Framework files
   */
  async createRobotTestPackage(testFiles: TestFile[], outputPath: string): Promise<string> {
    try {
      const packagePath = path.join(outputPath, 'robot-tests.zip');
      const output = fs.createWriteStream(packagePath);
      const archive = archiver('zip', { zlib: { level: 9 } });

      return new Promise((resolve, reject) => {
        output.on('close', () => {
          console.log(`✅ Test package created: ${archive.pointer()} bytes`);
          resolve(packagePath);
        });

        archive.on('error', reject);
        archive.pipe(output);

        // Add Robot Framework files to tests directory (REQUIRED by AWS Device Farm)
        // AWS Device Farm requires test files to have "test" in the name
        testFiles.forEach((file, index) => {
          let fileName = file.name;

          // Ensure filename contains "test" keyword for AWS Device Farm
          if (!fileName.toLowerCase().includes('test')) {
            const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '');
            const ext = fileName.includes('.') ? fileName.split('.').pop() : 'robot';
            fileName = `test_${nameWithoutExt}.${ext}`;
          }

          if (file.content) {
            archive.append(file.content, { name: `tests/${fileName}` });
          } else if (file.path) {
            archive.file(file.path, { name: `tests/${fileName}` });
          }
        });

        // Add requirements.txt for Robot Framework
        const requirements = `robotframework==6.1.1
          robotframework-appiumlibrary==2.0.0
          appium-python-client==3.1.0
          selenium==4.15.0`;

        archive.append(requirements, { name: 'requirements.txt' });

        // Add test spec YAML
        const testSpec = this.generateRobotTestSpec();
        archive.append(testSpec, { name: 'testspec.yml' });

        archive.finalize();
      });
    } catch (error) {
      console.error('Error creating test package:', error);
      throw error;
    }
  }

  /**
   * Upload file to Device Farm
   */
  async uploadFile(filePath: string, type: string, name: string): Promise<string> {
    try {
      // Create upload
      const createUploadParams = {
        projectArn: this.projectArn,
        name: name,
        type: type,
        contentType: 'application/octet-stream',
      };

      const uploadResponse = await this.deviceFarm.createUpload(createUploadParams).promise();
      const uploadArn = uploadResponse.upload!.arn!;
      const uploadUrl = uploadResponse.upload!.url!;

      console.log(`uploadUrl:`, uploadUrl);

      // Upload file to S3
      const fileContent = await fs.readFile(filePath);
      await axios.put(uploadUrl, fileContent, {
        headers: {
          'Content-Type': 'application/octet-stream',
        },
      });

      // Wait for upload to be processed
      await this.waitForUploadProcessing(uploadArn);
      return uploadArn;
    } catch (error) {
      console.error(`Error uploading ${type}:`, error);
      throw error;
    }
  }

  /**
   * Wait for upload processing
   */
  async waitForUploadProcessing(uploadArn: string, maxWaitTime: number = 300000): Promise<boolean> {
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      const upload = await this.deviceFarm.getUpload({ arn: uploadArn }).promise();
      const status = upload.upload!.status;

      if (status === 'SUCCEEDED') {
        return true;
      } else if (status === 'FAILED') {
        const errorMessage = upload.upload!.metadata
          ? JSON.parse(upload.upload!.metadata).errorMessage
          : upload.upload!.message;
        throw new Error(`Upload failed: ${errorMessage}`);
      }

      await new Promise((resolve) => setTimeout(resolve, 5000));
    }

    throw new Error('Upload processing timeout');
  }

  /**
   * Schedule test run on Device Farm
   */
  async scheduleRun(
    appArn: string,
    testPackageArn: string,
    runName: string,
    devicePoolArn: string,
    testSpecArn: string
  ): Promise<string> {
    try {
      const runParams = {
        projectArn: this.projectArn,
        appArn: appArn,
        devicePoolArn: devicePoolArn,
        name: runName,
        test: {
          type: 'APPIUM_PYTHON',
          testPackageArn: testPackageArn,
          testSpecArn: testSpecArn,
        },
        configuration: {
          billingMethod: 'METERED',
          locale: 'en_US',
          location: {
            latitude: 47.6204,
            longitude: -122.3491,
          },
        },
      };

      const runResponse = await this.deviceFarm.scheduleRun(runParams).promise();
      const runArn = runResponse.run!.arn!;

      return runArn;
    } catch (error) {
      console.error('Error scheduling run:', error);
      throw error;
    }
  }

  /**
   * Get available device pools
   */
  async getDevicePools(): Promise<AWS.DeviceFarm.DevicePool[]> {
    try {
      const devicePools = await this.deviceFarm
        .listDevicePools({
          arn: this.projectArn,
        })
        .promise();

      return devicePools.devicePools || [];
    } catch (error) {
      console.error('Error getting device pools:', error);
      throw error;
    }
  }

  /**
   * Get devices in a pool
   */
  async getDevicesInPool(devicePoolArn: string): Promise<AWS.DeviceFarm.DevicePool> {
    try {
      const devices = await this.deviceFarm
        .getDevicePool({
          arn: devicePoolArn,
        })
        .promise();

      return devices.devicePool!;
    } catch (error) {
      console.error('Error getting devices in pool:', error);
      throw error;
    }
  }

  /**
   * Monitor test run progress
   */
  async monitorRun(runArn: string, onProgress?: (progress: any) => void): Promise<any> {
    try {
      const run = await this.deviceFarm.getRun({ arn: runArn }).promise();
      const status = run.run!.status;
      const result = run.run!.result;

      const progress = {
        status,
        result,
        message: run.run!.message,
        totalJobs: run.run!.totalJobs,
        completedJobs: run.run!.completedJobs,
        elapsedTime: 0,
      };

      if (onProgress) {
        onProgress(progress);
      }

      return {
        status,
        result,
        run: run.run,
      };
    } catch (error) {
      console.error('Error monitoring run:', error);
      throw error;
    }
  }

  /**
   * Get test results and artifacts
   */
  async getTestResults(runArn: string): Promise<any[]> {
    try {
      // Get jobs
      const jobs = await this.deviceFarm.listJobs({ arn: runArn }).promise();
      const results = [];

      for (const job of jobs.jobs || []) {
        const jobResult = {
          device: job.device,
          status: job.status,
          result: job.result,
          message: job.message,
          suites: [],
        };

        results.push(jobResult);
      }

      return results;
    } catch (error) {
      console.error('Error getting test results:', error);
      throw error;
    }
  }
}

export default new AWSDeviceFarmService();
