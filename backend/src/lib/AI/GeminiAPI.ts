import { GoogleGenAI } from '@google/genai';

export interface ApiChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

interface GeminiContent {
  role: 'user' | 'model';
  parts: Array<{ text: string }>;
}

const mapToGeminiMessages = (apiMessages: ApiChatMessage[]): GeminiContent[] => {
  const geminiMessages: GeminiContent[] = [];
  let systemPromptsContent: string[] = [];

  for (const msg of apiMessages) {
    if (msg.role === 'system') {
      systemPromptsContent.push(msg.content);
    } else if (msg.role === 'user') {
      let userParts = [{ text: msg.content }];
      if (
        systemPromptsContent.length > 0 &&
        geminiMessages.filter((m) => m.role === 'user').length === 0
      ) {
        userParts = [...systemPromptsContent.map((sp) => ({ text: sp })), ...userParts];
        systemPromptsContent = [];
      }
      geminiMessages.push({ role: 'user', parts: userParts });
    } else if (msg.role === 'assistant') {
      geminiMessages.push({ role: 'model', parts: [{ text: msg.content }] });
    }
  }
  return geminiMessages;
};

export async function* streamGeminiChat(apiMessages: ApiChatMessage[]): AsyncGenerator<string> {
  const apiKey = Bun.env.GEMINI_API_KEY;
  if (!apiKey || apiKey === 'YOUR_GEMINI_API_KEY_HERE' || apiKey.trim() === '') {
    console.error('GEMINI_API_KEY is not set or is a placeholder.');
    yield 'Error: Gemini API Key is not configured correctly on the server. Please contact the administrator.';
    return;
  }

  try {
    const ai = new GoogleGenAI({ apiKey });
    const modelName = Bun.env.GEMINI_MODEL || 'gemini-2.0-flash-001';

    const generationConfig = {
      temperature: parseFloat(Bun.env.TEMPERATURE || '0.7'),
      topK: parseInt(Bun.env.TOP_K || '40', 10),
      topP: parseFloat(Bun.env.TOP_P || '0.8'),
      maxOutputTokens: parseInt(Bun.env.MAX_OUTPUT_TOKENS || '8192', 10),
    };

    const safetySettings = [
      {
        category: 'HARM_CATEGORY_HARASSMENT',
        threshold: Bun.env.SAFETY_HARASSMENT || 'BLOCK_MEDIUM_AND_ABOVE',
      },
      {
        category: 'HARM_CATEGORY_HATE_SPEECH',
        threshold: Bun.env.SAFETY_HATE_SPEECH || 'BLOCK_MEDIUM_AND_ABOVE',
      },
      {
        category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
        threshold: Bun.env.SAFETY_SEXUALLY_EXPLICIT || 'BLOCK_MEDIUM_AND_ABOVE',
      },
      {
        category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
        threshold: Bun.env.SAFETY_DANGEROUS_CONTENT || 'BLOCK_MEDIUM_AND_ABOVE',
      },
    ];

    const geminiFormattedMessages = mapToGeminiMessages(apiMessages);

    if (geminiFormattedMessages.length === 0) {
      console.error('No messages found for Gemini chat.');
      yield 'Error: No messages provided for Gemini chat.';
      return;
    }

    const response = await ai.models.generateContentStream({
      model: modelName,
      contents: geminiFormattedMessages,
      generationConfig,
      safetySettings,
    });

    for await (const chunk of response) {
      if (chunk && chunk.text) {
        yield chunk.text;
      }
    }

  } catch (error: any) {
    console.error('Error during Gemini API call:', error);
    let errorMessage = 'An error occurred while communicating with the AI model.';
    
    if (error.message) {
      errorMessage = `Gemini API Error: ${error.message}`;
    }
    
    if (error.toString().includes('API key not valid')) {
      errorMessage = 'Gemini API Error: API key not valid. Please check server configuration.';
    } else if (error.toString().includes('quota')) {
      errorMessage = 'Gemini API Error: Quota exceeded. Please check your Gemini API plan and limits.';
    } else if (error.toString().includes('SAFETY')) {
      errorMessage = 'Gemini API Error: Content was blocked by safety filters.';
    }

    yield errorMessage;
  }
}