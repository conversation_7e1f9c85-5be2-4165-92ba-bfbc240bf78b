/**
 * <PERSON>àm dùng để kiểm tra file có tồn tại không
 * @param param0
 * @returns
 */
export default async function CheckFileFromGithub({
  projectSlug,
  path,
}: {
  projectSlug: string;
  path: string;
}) {
  const checkRes = await fetch(
    `${Bun.env.GH_URTEST_WORKFLOW_API}/contents/tests/${projectSlug}/${path}`,
    {
      headers: {
        Authorization: `Bearer ${Bun.env.GH_TOKEN}`,
        Accept: 'application/vnd.github.v3+json',
      },
    }
  );

  if (checkRes.ok) return await checkRes.json();

  return null;
}
