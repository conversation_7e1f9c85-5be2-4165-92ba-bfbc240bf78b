import db from '../db/db';
import { ManualTestHistoryTable } from '../db/schema';
import dayjs from 'dayjs';
import { ulid } from 'ulid';

export type ManualTestActionType =
  | 'CREATE_TEST_CASE'
  | 'UPDATE_TEST_CASE'
  | 'DELETE_TEST_CASE'
  | 'EXECUTE_TEST_CASE'
  | 'UPDATE_TEST_CASE_STATUS'
  | 'CREATE_BUG'
  | 'UPDATE_BUG'
  | 'UPDATE_BUG_STATUS'
  | 'DELETE_BUG'
  | 'UPLOAD_FILE'
  | 'DELETE_FILE'
  | 'BULK_CREATE_TEST_CASES';

export type ManualTestTargetType = 'TEST_CASE' | 'BUG' | 'FILE' | 'PROJECT';

export type ManualTestHistoryMetadata = Record<string, any>;

export const logManualTestHistory = async (
  actionType: ManualTestActionType,
  targetType: ManualTestTargetType,
  targetId: string,
  projectId: string,
  userEmail: string,
  description: string,
  changes: ManualTestHistoryMetadata = {},
  metadata: ManualTestHistoryMetadata = {},
  testCaseId: string | null = null
): Promise<void> => {
  try {
    await db.insert(ManualTestHistoryTable).values({
      id: ulid(),
      actionType,
      targetType,
      targetId,
      projectId,
      testCaseId,
      userEmail,
      description,
      changes,
      metadata,
      createdAt: dayjs().toISOString(),
      createdBy: userEmail,
    });
  } catch (error) {
    console.error('Error logging manual test history:', error);
  }
};

export const ACTION_TYPES: Record<string, ManualTestActionType> = {
  CREATE_TEST_CASE: 'CREATE_TEST_CASE',
  UPDATE_TEST_CASE: 'UPDATE_TEST_CASE',
  DELETE_TEST_CASE: 'DELETE_TEST_CASE',
  EXECUTE_TEST_CASE: 'EXECUTE_TEST_CASE',
  UPDATE_TEST_CASE_STATUS: 'UPDATE_TEST_CASE_STATUS',
  CREATE_BUG: 'CREATE_BUG',
  UPDATE_BUG: 'UPDATE_BUG',
  UPDATE_BUG_STATUS: 'UPDATE_BUG_STATUS',
  DELETE_BUG: 'DELETE_BUG',
  UPLOAD_FILE: 'UPLOAD_FILE',
  DELETE_FILE: 'DELETE_FILE',
  BULK_CREATE_TEST_CASES: 'BULK_CREATE_TEST_CASES',
};
