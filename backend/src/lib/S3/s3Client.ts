import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { ulid } from 'ulid';

const s3Client = new S3Client({
  region: Bun.env.AWS_S3_REGION!,
  endpoint: `https://${Bun.env.AWS_S3_ENDPOINT}`,
  credentials: {
    accessKeyId: Bun.env.AWS_S3_ACCESS_KEY_ID!,
    secretAccessKey: Bun.env.AWS_S3_SECRET_ACCESS_KEY!,
  },
});

export interface UploadedFile {
  id: string;
  fileName: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  uploadedAt: string;
}

export const uploadFileToS3 = async (
  file: File,
  folder: string = 'bugs'
): Promise<UploadedFile> => {
  const fileId = ulid();
  const fileExtension = file.name.split('.').pop();
  const fileName = `${fileId}.${fileExtension}`;
  const key = `${folder}/${fileName}`;

  const uploadParams = {
    Bucket: Bun.env.AWS_S3_BUCKET!,
    Key: key,
    Body: await file.arrayBuffer(),
    ContentType: file.type,
  };

  await s3Client.send(new PutObjectCommand(uploadParams));

  const fileUrl = `${Bun.env.AWS_S3_CDN_PUBLIC_PATH}/${key}`;

  return {
    id: fileId,
    fileName,
    originalName: file.name,
    mimeType: file.type,
    size: file.size,
    url: fileUrl,
    uploadedAt: new Date().toISOString(),
  };
};

export const deleteFileFromS3 = async (
  fileName: string,
  folder: string = 'bugs'
): Promise<void> => {
  const key = `${folder}/${fileName}`;

  const deleteParams = {
    Bucket: Bun.env.AWS_S3_BUCKET!,
    Key: key,
  };

  await s3Client.send(new DeleteObjectCommand(deleteParams));
};

export const generatePresignedUrl = async (
  fileName: string,
  folder: string = 'bugs',
  expiresIn: number = 3600
): Promise<string> => {
  const key = `${folder}/${fileName}`;

  const command = new PutObjectCommand({
    Bucket: Bun.env.AWS_S3_BUCKET!,
    Key: key,
  });

  return await getSignedUrl(s3Client, command, { expiresIn });
};

export default s3Client;
