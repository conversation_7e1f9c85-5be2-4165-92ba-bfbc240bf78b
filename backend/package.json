{"name": "urdraw-workspace-be", "version": "1.0.0", "description": "Backend for managing UrTest", "main": "server.js", "scripts": {"dev": "bun --watch server.ts", "start": "bun server.ts", "test": "echo \"No tests yet\""}, "dependencies": {"@aws-sdk/client-s3": "^3.645.0", "@aws-sdk/s3-request-presigner": "^3.645.0", "@google/genai": "^1.3.0", "@hono/zod-validator": "^0.4.3", "@types/jsonwebtoken": "^9.0.9", "archiver": "^6.0.1", "aws-sdk": "^2.1692.0", "axios": "^1.9.0", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "drizzle-orm": "^0.41.0", "express": "^4.18.2", "express-validator": "^7.0.1", "flatted": "^3.3.3", "fs-extra": "^11.1.1", "hono": "^4.7.8", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "lodash": "^4.17.21", "mongoose": "^8.14.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "pg": "^8.15.6", "pg-hstore": "^2.3.4", "prettier": "^3.5.3", "sequelize": "^6.35.1", "ulid": "^3.0.0", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@types/bun": "latest", "@types/lodash": "^4.17.16", "@types/multer": "^1.4.12", "@types/pg": "^8.11.11", "drizzle-kit": "^0.30.6", "nodemon": "^3.0.1", "tsx": "^4.19.3"}, "private": true, "peerDependencies": {"typescript": "^5"}}