SERVER_PORT=3020
BACKEND_NODE_ENV=development

PG_DATABASE_URL=

KE<PERSON>CLOAK_URL=
KEYCLOAK_REALM=
KEYCLOAK_CLIENT_ID=

K<PERSON><PERSON>CLOA<PERSON>_ADMIN_CLIENT_ID=
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_ADMIN_CLIENT_SECRET=

MONGO_URI=

GH_URTEST_WORKFLOW_API=https://api.github.com/repos/dongitran/UrTest-Demo-Workflow
GH_TOKEN=

RUNNER_API_URL=
RUNNER_X_API_KEY=

DEEPSEEK_API_KEY=
DEEPSEEK_API_URL=https://api.deepseek.com/chat/completions

BACKEND_ATLASSIAN_CLIENT_ID=
BACKEND_ATLASSIAN_CLIENT_SECRET=

# Gemini API Configuration
GEMINI_API_KEY=
GEMINI_MODEL=
TEMPERATURE=0.7
TOP_K=40
TOP_P=0.8
MAX_OUTPUT_TOKENS=8192
SAFETY_HARASSMENT=BLOCK_MEDIUM_AND_ABOVE
SAFETY_HATE_SPEECH=BLOCK_MEDIUM_AND_ABOVE
SAFETY_SEXUALLY_EXPLICIT=BLOCK_MEDIUM_AND_ABOVE
SAFETY_DANGEROUS_CONTENT=BLOCK_MEDIUM_AND_ABOVE

# S3 Configuration
AWS_S3_BUCKET=
AWS_S3_ENDPOINT=
AWS_S3_ACCESS_KEY_ID=
AWS_S3_SECRET_ACCESS_KEY=
AWS_S3_CDN_PATH=
AWS_S3_REGION=
AWS_S3_CDN_PUBLIC_PATH=