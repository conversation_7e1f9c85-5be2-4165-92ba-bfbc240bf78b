# Backend Environment Configuration

# Server Configuration
PORT=3001
NODE_ENV=production

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/urtest_db

# JWT Configuration
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=24h

# AWS Configuration for Device Farm
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-west-2

# AWS Device Farm Configuration
AWS_DEVICE_FARM_PROJECT_ARN=arn:aws:devicefarm:us-west-2:158761644405:project:75798277-add6-4d91-8c1e-8e19845be3b6
AWS_DEVICE_FARM_DEVICE_POOL_ARN=arn:aws:devicefarm:us-west-2::devicepool:082d10e5-d7d7-48a5-ba5c-b33d66efa1f5

# S3 Configuration (for file uploads)
AWS_S3_BUCKET=urtest-uploads
AWS_S3_REGION=us-west-2

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# API Configuration
API_PREFIX=/api
API_VERSION=v1
