#!/usr/bin/env node

const emailTemplateService = require('./backend/src/services/emailTemplateService');

console.log('🧪 Testing Email Template Service');
console.log('==================================');

// Test 1: Basic placeholder replacement
console.log('\n1. Testing basic placeholder replacement...');
const template1 = 'Hello {{name}}, welcome to {{project}}!';
const variables1 = {
  name: '<PERSON>',
  project: 'UrTest'
};

const result1 = emailTemplateService.processTemplate(template1, variables1);
console.log('Template:', template1);
console.log('Variables:', variables1);
console.log('Result:', result1.content);
console.log('Success:', result1.success);

// Test 2: Multiple placeholder formats
console.log('\n2. Testing multiple placeholder formats...');
const template2 = 'Hello {{name}}, your ${status} is %result% and [count] tests passed.';
const variables2 = {
  name: '<PERSON>',
  status: 'test execution',
  result: 'successful',
  count: '15'
};

const result2 = emailTemplateService.processTemplate(template2, variables2);
console.log('Template:', template2);
console.log('Variables:', variables2);
console.log('Result:', result2.content);

// Test 3: Missing variables
console.log('\n3. Testing missing variables...');
const template3 = 'Hello {{name}}, your test {{missing_var}} completed.';
const variables3 = {
  name: 'Bob Wilson'
  // missing_var is not provided
};

const result3 = emailTemplateService.processTemplate(template3, variables3);
console.log('Template:', template3);
console.log('Variables:', variables3);
console.log('Result:', result3.content);
console.log('Unresolved:', result3.unresolved);
console.log('Warnings:', result3.warnings);

// Test 4: Strict mode
console.log('\n4. Testing strict mode...');
const result4 = emailTemplateService.processTemplate(template3, variables3, { strict: true });
console.log('Strict mode result:', result4.success);
console.log('Error:', result4.error);

// Test 5: Default values
console.log('\n5. Testing default values...');
const result5 = emailTemplateService.processTemplate(template3, variables3, { 
  defaultValue: '[NOT_PROVIDED]' 
});
console.log('With default value:', result5.content);

// Test 6: Built-in templates
console.log('\n6. Testing built-in templates...');
const testCompleteVars = {
  user_name: 'Alice Johnson',
  project_name: 'Mobile App Testing',
  test_suite_name: 'Login Tests',
  status: 'PASSED',
  duration: '5 minutes',
  passed_tests: '8',
  total_tests: '10',
  results_url: 'https://urtest.example.com/results/123'
};

const result6 = emailTemplateService.processTemplateByName('testComplete', testCompleteVars);
console.log('Test Complete Template:');
console.log(result6.content);

// Test 7: Device Farm template
console.log('\n7. Testing Device Farm template...');
const deviceFarmVars = {
  user_name: 'Charlie Brown',
  run_name: 'Android Smoke Tests',
  project_name: 'E-commerce App',
  device_pool: 'Top Android Devices',
  status: 'COMPLETED',
  total_devices: '5',
  passed_devices: '4',
  results_url: 'https://urtest.example.com/device-farm/results/456'
};

const result7 = emailTemplateService.processTemplateByName('deviceFarmComplete', deviceFarmVars);
console.log('Device Farm Template:');
console.log(result7.content);

// Test 8: Invalid template name
console.log('\n8. Testing invalid template name...');
const result8 = emailTemplateService.processTemplateByName('nonExistentTemplate', {});
console.log('Invalid template result:', result8.success);
console.log('Error:', result8.error);

// Test 9: Variable validation
console.log('\n9. Testing variable validation...');
const invalidVars = {
  name: 'Valid Name',
  nullVar: null,
  undefinedVar: undefined,
  emptyVar: '',
  objectVar: { invalid: true }
};

const validation = emailTemplateService.validateVariables(invalidVars);
console.log('Validation result:', validation);

// Test 10: Find unresolved placeholders
console.log('\n10. Testing unresolved placeholder detection...');
const templateWithUnresolved = 'Hello {{name}}, your {{status}} is {{result}} and {{unresolved}} remains.';
const partialVars = {
  name: 'Test User',
  status: 'test'
};

const unresolved = emailTemplateService.findUnresolvedPlaceholders(
  emailTemplateService.replacePlaceholders(templateWithUnresolved, partialVars)
);
console.log('Template:', templateWithUnresolved);
console.log('Partial variables:', partialVars);
console.log('Unresolved placeholders:', unresolved);

console.log('\n✅ Email Template Service testing completed!');

// Example usage in real application
console.log('\n📧 Example usage in email service:');
console.log(`
// In your email service:
const emailTemplateService = require('./services/emailTemplateService');

async function sendTestCompleteEmail(userEmail, testData) {
  const variables = {
    user_name: testData.userName,
    project_name: testData.projectName,
    test_suite_name: testData.testSuiteName,
    status: testData.status,
    duration: testData.duration,
    passed_tests: testData.passedTests,
    total_tests: testData.totalTests,
    results_url: testData.resultsUrl
  };

  const result = emailTemplateService.processTemplateByName('testComplete', variables, {
    strict: false,
    logWarnings: true,
    defaultValue: 'N/A'
  });

  if (result.success) {
    await sendEmail(userEmail, 'Test Complete', result.content);
  } else {
    console.error('Template processing failed:', result.error);
  }
}
`);
