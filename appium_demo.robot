*** Settings ***
Documentation  Simple example using AppiumLibrary
Library  AppiumLibrary
Test Tags  webdriver
Suite Setup  Open Test Application

*** Variables ***
${ANDROID_AUTOMATION_NAME}    UIAutomator2
${ANDROID_APP}                /Users/<USER>/Downloads/Telegram Desktop/UrBox_development (2).apk
${ANDROID_PLATFORM_NAME}      Android
${ANDROID_PLATFORM_VERSION}   16
${APPIUM SERVER URL}  http://127.0.0.1:4723 
${WAIT_TIME_OUT}  50

*** Test Cases ***
user should be able to toggle between menus in the app
  user should be on login 
  user should be on home page
  user should be on "Nạp tiền điện thoại" pageage
  Close Application

*** Keywords ***
Open Test Application
  Open Application  ${APPIUM SERVER URL}  automationName=${ANDROID_AUTOMATION_NAME}
  ...  platformName=${ANDROID_PLATFORM_NAME}  platformVersion=${ANDROID_PLATFORM_VERSION}
  ...  app=${ANDROID_APP}  appWaitActivity=*

click ${menu name} page
    click element  xpath=//android.widget.TextView[@text="${menu name}"]

click button
    [Arguments]  ${button_text}
    Wait Until Page Contains Element  xpath=//android.widget.Button[@text="${button_text}"]  timeout=${WAIT_TIME_OUT}
    Click Element  xpath=//android.widget.Button[@text="${button_text}"]

Check And Click Button If Exists
    [Arguments]  ${button_text}
    ${button_exists}=  Run Keyword And Return Status  Page Should Contain Element  xpath=//android.widget.Button[@text="${button_text}"]  timeout=${WAIT_TIME_OUT}
    Run Keyword If  ${button_exists}  Run Keywords
    ...  Element Should Be Enabled  xpath=//android.widget.Button[@text="${button_text}"]
    ...  AND  Click Element  xpath=//android.widget.Button[@text="${button_text}"]
    ...  ELSE  Log  Button with text "${button_text}" not found

Input Text Into EditText
    [Arguments]  ${locator}  ${input_text}
    Wait Until Page Contains Element  ${locator}  timeout=${WAIT_TIME_OUT}
    Scroll To Element  ${locator}
    Element Should Be Enabled  ${locator}
    Clear Text  ${locator}
    Input Text  ${locator}  ${input_text}
    Hide Keyboard

Scroll To Element
    [Arguments]  ${locator}
    Swipe  500  1000  500  200  1000  # Adjust coordinates based on your device screen
    ${exists}=  Run Keyword And Return Status  Page Should Contain Element  ${locator}  timeout=2s
    Run Keyword If  not ${exists}  Scroll To Element  ${locator}  # Recursive scroll until found

Input Text Into EditText By ViewGroup
    [Arguments]  ${text_view_content}  ${input_text}
    # Kiểm tra EditText trong ViewGroup cha trước
    ${locator_parent}=  Set Variable  xpath=//android.view.ViewGroup[./android.widget.TextView[@text="${text_view_content}"]]//android.widget.EditText
    ${exists_parent}=  Run Keyword And Return Status  Wait Until Page Contains Element  ${locator_parent}  timeout=${WAIT_TIME_OUT}
    
    # Nếu tìm thấy trong ViewGroup cha
    Run Keyword If  ${exists_parent}  Run Keywords
    ...  Log  Found EditText in parent ViewGroup for TextView "${text_view_content}"
    ...  AND  Element Should Be Enabled  ${locator_parent}
    ...  AND  Clear Text  ${locator_parent}
    ...  AND  Input Text  ${locator_parent}  ${input_text}
    ...  AND  Hide Keyboard
    ...  AND  Return From Keyword
    
    # Nếu không tìm thấy trong ViewGroup cha, kiểm tra trong cấu trúc nested qua ScrollView
    ${locator_nested}=  Set Variable  xpath=//android.view.ViewGroup[./android.widget.TextView[@text="${text_view_content}"]]//android.widget.ScrollView//android.view.ViewGroup//android.widget.EditText
    ${exists_nested}=  Run Keyword And Return Status  Wait Until Page Contains Element  ${locator_nested}  timeout=${WAIT_TIME_OUT}
    
    Run Keyword If  ${exists_nested}  Run Keywords
    ...  Log  Found EditText in nested ViewGroup via ScrollView for TextView "${text_view_content}"
    ...  AND  Element Should Be Enabled  ${locator_nested}
    ...  AND  Clear Text  ${locator_nested}
    ...  AND  Input Text  ${locator_nested}  ${input_text}
    ...  AND  Hide Keyboard
    ...  ELSE  Fail  EditText associated with TextView "${text_view_content}" not found in parent or nested ViewGroup via ScrollView

user should be on login 
  Wait Until Page Contains Element  xpath=//android.widget.TextView[@text="Nhập số điện thoại"]  timeout=${WAIT_TIME_OUT}
  Input Text Into EditText By ViewGroup  Số điện thoại  0909000555
  Click Element  xpath=//android.widget.FrameLayout[@resource-id="android:id/content"]/android.widget.FrameLayout/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.widget.FrameLayout/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[2]/android.view.ViewGroup/android.view.ViewGroup[1]/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[3]/android.view.ViewGroup[1]/android.view.ViewGroup/android.view.ViewGroup
  Wait Until Page Contains  Nhập mật khẩu  timeout=${WAIT_TIME_OUT}    
  Scroll To Element  xpath=//android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[1]//android.widget.EditText
  Input Text Into EditText  xpath=//android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[1]//android.widget.EditText  111111

user should be on home page
  Scroll To Element  xpath=//android.widget.TextView[@text="Dịch vụ và tiện ích khác"]
