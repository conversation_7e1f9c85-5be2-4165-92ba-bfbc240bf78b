ALTER TABLE tbl_bugs 
ADD COLUMN attachments jsonb DEFAULT '[]'::jsonb;


CREATE TYPE manual_test_history_action_type AS ENUM (
    'CREATE_TEST_CASE',
    'UPDATE_TEST_CASE', 
    'DELETE_TEST_CASE',
    'EXECUTE_TEST_CASE',
    'UPDATE_TEST_CASE_STATUS',
    'CREATE_BUG',
    'UPDATE_BUG',
    'UPDATE_BUG_STATUS',
    'DELETE_BUG',
    'UPLOAD_FILE',
    'DELETE_FILE',
    'BULK_CREATE_TEST_CASES'
);

CREATE TYPE manual_test_history_target_type AS ENUM (
    'TEST_CASE',
    'BUG', 
    'FILE',
    'PROJECT'
);

CREATE TABLE tbl_manual_test_history (
    id VARCHAR(255) PRIMARY KEY NOT NULL,
    action_type manual_test_history_action_type NOT NULL,
    target_type manual_test_history_target_type NOT NULL,
    target_id VARCHAR(255) NOT NULL,
    project_id VARCHAR(255) NOT NULL,
    test_case_id VARCHAR(255),
    user_email VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    changes JSONB,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by VARCHAR(255)
);

CREATE INDEX idx_manual_test_history_project_id ON tbl_manual_test_history(project_id);
CREATE INDEX idx_manual_test_history_test_case_id ON tbl_manual_test_history(test_case_id);
CREATE INDEX idx_manual_test_history_user_email ON tbl_manual_test_history(user_email);
CREATE INDEX idx_manual_test_history_action_type ON tbl_manual_test_history(action_type);
CREATE INDEX idx_manual_test_history_target_type ON tbl_manual_test_history(target_type);
CREATE INDEX idx_manual_test_history_created_at ON tbl_manual_test_history(created_at DESC);

CREATE INDEX idx_manual_test_history_project_created_at ON tbl_manual_test_history(project_id, created_at DESC);
CREATE INDEX idx_manual_test_history_testcase_created_at ON tbl_manual_test_history(test_case_id, created_at DESC) WHERE test_case_id IS NOT NULL;
CREATE INDEX idx_manual_test_history_project_action_created_at ON tbl_manual_test_history(project_id, action_type, created_at DESC);

ALTER TABLE tbl_manual_test_history 
ADD CONSTRAINT fk_manual_test_history_project_id 
FOREIGN KEY (project_id) REFERENCES tbl_projects(id);

ALTER TABLE tbl_manual_test_history 
ADD CONSTRAINT fk_manual_test_history_test_case_id 
FOREIGN KEY (test_case_id) REFERENCES tbl_manual_test_cases(id);
